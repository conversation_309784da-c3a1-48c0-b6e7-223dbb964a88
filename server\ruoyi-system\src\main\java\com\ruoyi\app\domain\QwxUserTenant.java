package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户租户关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QwxUserTenant implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 全局用户ID
     */
    @ApiModelProperty(value = "全局用户ID", example = "1234567890")
    private String globalUserId;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", example = "1")
    private Long tenantId;

    /**
     * 租户内用户ID
     */
    @ApiModelProperty(value = "租户内用户ID", example = "1001")
    private Long userId;

    /**
     * 租户内昵称
     */
    @ApiModelProperty(value = "租户内昵称", example = "张三")
    private String nickName;

    /**
     * 头像地址
     */
    @ApiModelProperty(value = "头像地址")
    private String avatar;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位", example = "项目经理")
    private String position;

    /**
     * 加入时间
     */
    @ApiModelProperty(value = "加入时间", example = "2025-05-15 10:00:00")
    private Date joinTime;

    /**
     * 状态（0正常 1停用）
     */
    @ApiModelProperty(value = "状态", example = "0", notes = "0正常 1停用")
    private String status;

    /**
     * 在线状态（0离线 1在线 2隐身）
     */
    @ApiModelProperty(value = "在线状态", example = "0", notes = "0离线 1在线 2隐身")
    private String onlineStatus;

    /**
     * 是否为管理员（0否 1是）
     */
    @ApiModelProperty(value = "是否为管理员", example = "0", notes = "0否 1是")
    private String isAdmin;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2025-05-15 10:00:00")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", example = "2025-05-15 10:00:00")
    private Date updateTime;

    /**
     * 是否已退出组织（0否 1是）
     */
    @ApiModelProperty(value = "是否已退出组织", example = "0", notes = "0否 1是")
    private String isQuit;
}
