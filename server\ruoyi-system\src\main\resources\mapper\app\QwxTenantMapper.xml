<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.QwxTenantMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.QwxTenant">
        <id column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="tenant_code" property="tenantCode" />
        <result column="logo" property="logo" />
        <result column="industry" property="industry" />
        <result column="scale" property="scale" />
        <result column="address" property="address" />
        <result column="contact_user" property="contactUser" />
        <result column="contact_phone" property="contactPhone" />
        <result column="domain" property="domain" />
        <result column="intro" property="intro" />
        <result column="status" property="status" />
        <result column="del_flag" property="delFlag" />
        <result column="expire_time" property="expireTime" />
        <result column="account_count" property="accountCount" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>

     <sql id="selectQwxTenantVo">
        select tenant_id, tenant_name, tenant_code, logo, industry, scale, address, contact_user, contact_phone, domain, intro, status, del_flag, expire_time, account_count, create_user_id, create_time, update_time, remark from qwx_tenant
    </sql>

    <select id="selectQwxTenantList" parameterType="QwxTenant" resultMap="BaseResultMap">
        <include refid="selectQwxTenantVo"/>
        <where>
            <if test="tenantName != null  and tenantName != ''"> and tenant_name like concat('%', #{tenantName}, '%')</if>
            <if test="tenantCode != null  and tenantCode != ''"> and tenant_code = #{tenantCode}</if>
            <if test="logo != null  and logo != ''"> and logo = #{logo}</if>
            <if test="industry != null  and industry != ''"> and industry = #{industry}</if>
            <if test="scale != null  and scale != ''"> and scale = #{scale}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="contactUser != null  and contactUser != ''"> and contact_user = #{contactUser}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="domain != null  and domain != ''"> and domain = #{domain}</if>
            <if test="intro != null  and intro != ''"> and intro = #{intro}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="expireTime != null "> and expire_time = #{expireTime}</if>
            <if test="accountCount != null "> and account_count = #{accountCount}</if>
            <if test="createUserId != null  and createUserId != ''"> and create_user_id = #{createUserId}</if>
        </where>
    </select>

    <select id="selectQwxTenantByTenantId" parameterType="Long" resultMap="BaseResultMap">
        <include refid="selectQwxTenantVo"/>
        where tenant_id = #{tenantId}
    </select>

    <select id="selectDefaultTenantByUserId" resultMap="BaseResultMap">
        SELECT 
            t.*
        FROM 
            qwx_tenant t
        INNER JOIN 
            qwx_user_tenant ut ON t.tenant_id = ut.tenant_id
        WHERE 
            ut.global_user_id = #{globalUserId}
            AND ut.is_default = '1' 
            AND t.status = '0' 
            AND t.del_flag = '0'
    </select>
</mapper>
