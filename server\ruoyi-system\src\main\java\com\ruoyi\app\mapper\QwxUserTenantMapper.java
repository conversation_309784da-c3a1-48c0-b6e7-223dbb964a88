package com.ruoyi.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.app.domain.QwxUserTenant;
import com.ruoyi.app.model.vo.AppUserTenantIMVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户租户关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
public interface QwxUserTenantMapper extends BaseMapper<QwxUserTenant> {
    
    /**
     * 获取用户的所有租户及云信账号信息
     *
     * @param globalUserId 全局用户ID
     * @return 租户和云信账号信息列表
     */
    List<AppUserTenantIMVo.TenantIMInfo> selectUserTenantAndIMInfoList(@Param("globalUserId") String globalUserId);

    /**
     * 获取用户的指定租户及云信账号信息
     *
     * @param globalUserId 全局用户ID
     * @param tenantId 租户ID
     * @return 租户和云信账号信息
     */
    AppUserTenantIMVo.TenantIMInfo selectSingleUserTenantAndIMInfo(@Param("globalUserId") String globalUserId, @Param("tenantId") String tenantId);
}
