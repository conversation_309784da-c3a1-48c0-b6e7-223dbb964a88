package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 实例授权历史表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QwxInstanceAuthHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 历史ID
     */
    @TableId(value = "history_id", type = IdType.AUTO)
    private Long historyId;

    /**
     * 实例ID
     */
    private Long instanceId;

    /**
     * Token ID
     */
    private Long tokenId;

    /**
     * 授权类型(init初始化,renew续期,revoke吊销)
     */
    private String authType;

    /**
     * 授权结果(0成功 1失败)
     */
    private String authResult;

    /**
     * 授权时间
     */
    private Date authTime;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private Date createTime;


}
