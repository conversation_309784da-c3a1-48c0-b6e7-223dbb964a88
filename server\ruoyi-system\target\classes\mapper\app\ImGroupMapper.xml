<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.ImGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.ImGroup">
        <id column="group_id" property="groupId" />
        <result column="tenant_id" property="tenantId" />
        <result column="group_name" property="groupName" />
        <result column="group_avatar" property="groupAvatar" />
        <result column="owner_id" property="ownerId" />
        <result column="introduction" property="introduction" />
        <result column="announcement" property="announcement" />
        <result column="member_count" property="memberCount" />
        <result column="max_member_count" property="maxMemberCount" />
        <result column="join_mode" property="joinMode" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        group_id, tenant_id, group_name, group_avatar, owner_id, introduction, announcement, member_count, max_member_count, join_mode, status, create_time, update_time
    </sql>

</mapper>
