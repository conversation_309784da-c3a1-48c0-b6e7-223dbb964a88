package com.ruoyi.web.service;

import com.ruoyi.app.domain.QwxGlobalUser;
import com.ruoyi.app.domain.QwxUserTenant;
import com.ruoyi.app.model.dto.LoginDto;
import com.ruoyi.app.model.vo.AppLoginResultVo;
import com.ruoyi.app.service.QwxGlobalUserService;
import com.ruoyi.app.service.QwxUserTenantService;
import com.ruoyi.common.constant.AppRedisConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.web.utils.AppContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 登录服务类
 *
 * <AUTHOR>
 * @date 2025/5/10 下午11:22
 */
@Slf4j
@Service
public class LoginService {


    @Resource
    private QwxGlobalUserService qwxGlobalUserService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private SmsService smsService;
    @Resource
    private EmailService emailService;
    @Resource
    private AppUserTokenService appUserTokenService;
    @Resource
    private QwxUserTenantService qwxUserTenantService;

    /**
     * 登录，支持手机号/邮箱登录，没账号就新增注册
     *
     * @param loginDto 登录参数
     * @return 登录结果
     */
    public R<AppLoginResultVo> login(LoginDto loginDto) {
        // 1. 验证码校验
        String code = loginDto.getCode();
        if (StringUtils.isEmpty(code)) {
            return R.fail("验证码不能为空");
        }

        // 万能验证码校验，方便开发测试
        boolean isValidCode = "1234".equals(code);

        // 如果不是万能验证码，则进行正常验证
        if (!isValidCode) {
            isValidCode = verifyCode(loginDto);
            if (!isValidCode) {
                return R.fail("验证码错误或已过期");
            }
        }

        // 2. 获取登录方式（手机号/邮箱）
        String mobile = loginDto.getMobile();
        String email = loginDto.getEmail();

        if (StringUtils.isEmpty(mobile) && StringUtils.isEmpty(email)) {
            return R.fail("手机号或邮箱不能为空");
        }

        // 3. 确定登录账号和类型
        String loginAccount = StringUtils.isNotEmpty(mobile) ? mobile : email;
        String registerType = StringUtils.isNotEmpty(mobile) ? "phone" : "email";

        try {
            // 4. 查询全局用户是否存在
            QwxGlobalUser globalUser;

            // 根据登录方式查询用户
            if ("phone".equals(registerType)) {
                globalUser = qwxGlobalUserService.lambdaQuery()
                        .eq(QwxGlobalUser::getUserName, mobile)
                        .eq(QwxGlobalUser::getPhone, mobile)
                        .eq(QwxGlobalUser::getDelFlag, "0")
                        .last("LIMIT 1")
                        .one();
            } else {
                globalUser = qwxGlobalUserService.lambdaQuery()
                        .eq(QwxGlobalUser::getUserName, email)
                        .eq(QwxGlobalUser::getEmail, email)
                        .eq(QwxGlobalUser::getDelFlag, "0")
                        .last("LIMIT 1")
                        .one();
            }

            // 5. 用户不存在，则注册新用户
            if (globalUser == null) {
                globalUser = new QwxGlobalUser();
                // 生成全局用户ID
                String globalUserId = IdUtils.fastSimpleUUID();
                globalUser.setGlobalUserId(globalUserId);

                // 设置基本信息
                globalUser.setUserName(loginAccount);

                if ("phone".equals(registerType)) {
                    globalUser.setPhone(mobile);
                } else {
                    globalUser.setEmail(email);
                }

                // 设置其他默认信息
                globalUser.setRegisterType(registerType);
                globalUser.setRegisterTime(new Date());
                globalUser.setStatus("0"); // 正常状态
                globalUser.setDelFlag("0"); // 未删除
                globalUser.setCreateTime(new Date());

                // 保存用户
                boolean saveResult = qwxGlobalUserService.save(globalUser);
                if (!saveResult) {
                    return R.fail("用户注册失败");
                }
            }

            // 6. 更新用户最后登录时间
            globalUser.setLastLoginTime(new Date());
            qwxGlobalUserService.updateById(globalUser);

            // 7. 使用APP Token服务生成token
            AppLoginResultVo loginResult = appUserTokenService.generateLoginResult(globalUser);

            return R.ok(loginResult);

        } catch (Exception e) {
            e.printStackTrace();
            return R.fail("登录失败：" + e.getMessage());
        }
    }

    /**
     * 发送验证码（支持短信和邮箱）
     *
     * @param loginDto 登录参数，包含手机号/邮箱和验证码类型
     * @return 操作结果
     */
    public AjaxResult sendCode(LoginDto loginDto) {
        // 获取验证码类型，默认为短信验证码
        String codeType = StringUtils.defaultIfEmpty(loginDto.getCodeType(), AppRedisConstants.CODE_TYPE_SMS);

        // 根据验证码类型发送不同的验证码
        if (AppRedisConstants.CODE_TYPE_SMS.equals(codeType)) {
            return sendSmsVerifyCode(loginDto);
        } else if (AppRedisConstants.CODE_TYPE_EMAIL.equals(codeType)) {
            return sendEmailVerifyCode(loginDto);
        } else {
            return AjaxResult.error("不支持的验证码类型");
        }
    }

    /**
     * 发送短信验证码
     *
     * @param loginDto 登录参数，包含手机号
     * @return 操作结果
     */
    private AjaxResult sendSmsVerifyCode(LoginDto loginDto) {
        // 验证参数
        String mobile = loginDto.getMobile();
        if (StringUtils.isEmpty(mobile)) {
            return AjaxResult.error("手机号不能为空");
        }

        // 验证手机号格式
        if (!isMobileValid(mobile)) {
            return AjaxResult.error("手机号格式不正确");
        }

        // 生成6位随机验证码
        String code = generateRandomCode(6);

        // 检查是否频繁发送
        String codeKey = AppRedisConstants.SMS_CODE_PREFIX + mobile;
        Object existCode = redisTemplate.opsForValue()
                .get(codeKey);
        if (existCode != null) {
            // 获取剩余过期时间
            Long expireTime = redisTemplate.getExpire(codeKey, TimeUnit.SECONDS);
            if (expireTime != null && expireTime > 240) { // 如果距离上次发送不足1分钟
                return AjaxResult.error("发送过于频繁，请稍后再试");
            }
        }

        try {
            // 调用短信发送接口
            boolean sendResult = sendSmsCode(mobile, code);

            if (sendResult) {
                // 将验证码保存到Redis，设置过期时间
                redisTemplate.opsForValue()
                        .set(codeKey, code, AppRedisConstants.CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
                return AjaxResult.success("短信验证码发送成功");
            } else {
                return AjaxResult.error("短信验证码发送失败，请稍后再试");
            }
        } catch (Exception e) {
            return AjaxResult.error("短信验证码发送异常: " + e.getMessage());
        }
    }

    /**
     * 发送邮箱验证码
     *
     * @param loginDto 登录参数，包含邮箱
     * @return 操作结果
     */
    private AjaxResult sendEmailVerifyCode(LoginDto loginDto) {
        // 验证参数
        String email = loginDto.getEmail();
        if (StringUtils.isEmpty(email)) {
            return AjaxResult.error("邮箱不能为空");
        }

        // 验证邮箱格式
        if (!isEmailValid(email)) {
            return AjaxResult.error("邮箱格式不正确");
        }

        // 生成6位随机验证码
        String code = generateRandomCode(6);

        // 检查是否频繁发送
        String codeKey = AppRedisConstants.EMAIL_CODE_PREFIX + email;
        Object existCode = redisTemplate.opsForValue()
                .get(codeKey);
        if (existCode != null) {
            // 获取剩余过期时间
            Long expireTime = redisTemplate.getExpire(codeKey, TimeUnit.SECONDS);
            if (expireTime != null && expireTime > 240) { // 如果距离上次发送不足1分钟
                return AjaxResult.error("发送过于频繁，请稍后再试");
            }
        }

        try {
            // 调用邮件发送接口
            boolean sendResult = sendEmailCode(email, code);

            if (sendResult) {
                // 将验证码保存到Redis，设置过期时间
                redisTemplate.opsForValue()
                        .set(codeKey, code, AppRedisConstants.CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
                return AjaxResult.success("邮箱验证码发送成功");
            } else {
                return AjaxResult.error("邮箱验证码发送失败，请稍后再试");
            }
        } catch (Exception e) {
            return AjaxResult.error("邮箱验证码发送异常: " + e.getMessage());
        }
    }

    /**
     * 验证短信验证码
     *
     * @param mobile 手机号
     * @param code   验证码
     * @return 验证结果
     */
    public boolean verifySmsCode(String mobile, String code) {
        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(code)) {
            return false;
        }

        String codeKey = AppRedisConstants.SMS_CODE_PREFIX + mobile;
        Object cacheCode = redisTemplate.opsForValue()
                .get(codeKey);

        if (cacheCode != null && code.equals(cacheCode.toString())) {
            // 验证成功后删除验证码
            redisTemplate.delete(codeKey);
            return true;
        }

        return false;
    }

    /**
     * 验证邮箱验证码
     *
     * @param email 邮箱
     * @param code  验证码
     * @return 验证结果
     */
    public boolean verifyEmailCode(String email, String code) {
        if (StringUtils.isEmpty(email) || StringUtils.isEmpty(code)) {
            return false;
        }

        String codeKey = AppRedisConstants.EMAIL_CODE_PREFIX + email;
        Object cacheCode = redisTemplate.opsForValue()
                .get(codeKey);

        if (cacheCode != null && code.equals(cacheCode.toString())) {
            // 验证成功后删除验证码
            redisTemplate.delete(codeKey);
            return true;
        }

        return false;
    }

    /**
     * 验证验证码（支持短信和邮箱）
     *
     * @param loginDto 登录参数
     * @return 验证结果
     */
    public boolean verifyCode(LoginDto loginDto) {
        String codeType = StringUtils.defaultIfEmpty(loginDto.getCodeType(), AppRedisConstants.CODE_TYPE_SMS);

        if (AppRedisConstants.CODE_TYPE_SMS.equals(codeType)) {
            return verifySmsCode(loginDto.getMobile(), loginDto.getCode());
        } else if (AppRedisConstants.CODE_TYPE_EMAIL.equals(codeType)) {
            return verifyEmailCode(loginDto.getEmail(), loginDto.getCode());
        }

        return false;
    }

    /**
     * 发送短信验证码
     *
     * @param mobile 手机号
     * @param code   验证码
     * @return 发送结果
     */
    private boolean sendSmsCode(String mobile, String code) {
        // 调用短信服务发送验证码
        return smsService.sendSmsCode(mobile, code);
    }

    /**
     * 发送邮箱验证码
     *
     * @param email 邮箱
     * @param code  验证码
     * @return 发送结果
     */
    private boolean sendEmailCode(String email, String code) {
        // 调用邮件服务发送验证码
        return emailService.sendEmailCode(email, code);
    }

    /**
     * 验证手机号格式是否正确
     *
     * @param mobile 手机号
     * @return 验证结果
     */
    private boolean isMobileValid(String mobile) {
        return mobile.matches("^1[3-9]\\d{9}$");
    }

    /**
     * 验证邮箱格式是否正确
     *
     * @param email 邮箱
     * @return 验证结果
     */
    private boolean isEmailValid(String email) {
        return email.matches("^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$");
    }

    /**
     * 生成指定位数的随机数字验证码
     *
     * @param length 验证码长度
     * @return 随机验证码
     */
    private String generateRandomCode(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append((int) (Math.random() * 10));
        }
        return sb.toString();
    }



    /**
     * 用户注销
     *
     * @return 操作结果
     */
    public AjaxResult logout() {
        try {
            // 获取当前用户信息
            QwxGlobalUser user = AppContext.getAppLoginUser();
            if (user == null) {
                return AjaxResult.error("用户未登录");
            }

            // 获取当前token
            String token = AppContext.getCurrentTenant() != null ? 
                    AppContext.getCurrentTenant().getToken() : null;
            if (StringUtils.isEmpty(token)) {
                return AjaxResult.error("无效的令牌");
            }

            // 注销租户登录（如果是租户token）
            boolean logoutResult = appUserTokenService.logoutTenant(token);

            if (logoutResult) {
              // 删除用户token
              appUserTokenService.removeUserToken(user.getGlobalUserId());
              return AjaxResult.success("注销成功");
            }
            return AjaxResult.error("注销失败");
        } catch (Exception e) {
            return AjaxResult.error("注销失败：" + e.getMessage());
        }
    }

    /**
     * 刷新Token
     * 
     * @param refreshToken 刷新令牌
     * @return 刷新后的token信息
     */
    public R<AppLoginResultVo> refreshToken(String refreshToken) {
        try {
            if (StringUtils.isEmpty(refreshToken)) {
                return R.fail("刷新令牌不能为空");
            }
            
            // 使用AppUserTokenService的refreshTokens方法刷新令牌
            AppLoginResultVo loginResult = appUserTokenService.refreshTokens(refreshToken);
            
            return R.ok(loginResult);
        } catch (Exception e) {
            log.error("刷新token失败", e);
            return R.fail("刷新token失败：" + e.getMessage());
        }
    }
}
