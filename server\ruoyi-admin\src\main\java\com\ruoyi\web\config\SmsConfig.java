package com.ruoyi.web.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 短信配置类
 * 
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "sms")
public class SmsConfig {
    /** 是否启用 */
    private boolean enabled = true;

    /** 短信服务商类型（aliyun/tencent/other） */
    private String type = "default";

    /** 短信签名 */
    private String signName;

    /** 短信模板ID */
    private String templateId;

    /** 短信服务商访问密钥ID */
    private String accessKeyId;

    /** 短信服务商访问密钥 */
    private String accessKeySecret;

    /** 短信发送超时时间（秒） */
    private int timeout = 10;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSignName() {
        return signName;
    }

    public void setSignName(String signName) {
        this.signName = signName;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }
} 