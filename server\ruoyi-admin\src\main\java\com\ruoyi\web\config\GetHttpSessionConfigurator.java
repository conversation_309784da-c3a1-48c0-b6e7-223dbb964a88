package com.ruoyi.web.config;

import javax.websocket.HandshakeResponse;
import javax.websocket.server.HandshakeRequest;
import javax.websocket.server.ServerEndpointConfig;
import java.util.List;
import java.util.Map;

public class GetHttpSessionConfigurator extends ServerEndpointConfig.Configurator {

    public static final String HTTP_SESSION_KEY = "HTTP_SESSION"; // Or any key you prefer for storing headers/token
    public static final String AUTHORIZATION_HEADER = "Authorization"; // Common header for tokens

    @Override
    public void modifyHandshake(ServerEndpointConfig sec, HandshakeRequest request, HandshakeResponse response) {
        super.modifyHandshake(sec, request, response);

        // 获取所有请求头
        Map<String, List<String>> headers = request.getHeaders();
        System.out.println("Handshake Headers: " + headers); // For debugging

        // 获取特定的 Authorization 头部
        List<String> authHeaders = headers.get(AUTHORIZATION_HEADER);
        if (authHeaders != null && !authHeaders.isEmpty()) {
            String tokenWithBearer = authHeaders.get(0); // 通常是 "Bearer your_token_here"
            System.out.println(AUTHORIZATION_HEADER + ": " + tokenWithBearer);

            // 你可能想提取纯 token
            String token = extractToken(tokenWithBearer);
            if (tokenWithBearer != null) {
                // 将 token 存储到 ServerEndpointConfig 的用户属性中
                // 稍后可以通过 Session.getUserProperties() 在 @OnOpen 中获取
                sec.getUserProperties().put("AUTH_TOKEN", token);
                System.out.println("Stored token: " + token);
            } else {
                // 如果 token 格式不正确或不存在，你可以在这里处理
                // 例如，可以修改 HandshakeResponse 来拒绝连接，但这更复杂
                // 更简单的是在 @OnOpen 中检查并关闭连接
                System.out.println("Token not found or malformed in Authorization header.");
            }
        } else {
            System.out.println(AUTHORIZATION_HEADER + " header not found.");
        }

        // 如果你想把整个 HTTP Session (如果存在且可用) 放入，可以这样做 (取决于你的容器)
        // HttpSession httpSession = (HttpSession) request.getHttpSession();
        // if (httpSession != null) {
        //     sec.getUserProperties().put(HTTP_SESSION_KEY, httpSession);
        // }
    }

    // 辅助方法，用于从 "Bearer <token>" 中提取 <token>
    private String extractToken(String authorizationHeader) {
        if (authorizationHeader != null && authorizationHeader.toLowerCase().startsWith("bearer ")) {
            return authorizationHeader.substring(7); // "Bearer " 后面的部分
        }
        // 如果你的token不是以Bearer开头，直接返回
        return authorizationHeader;
        // return null; // 或者根据你的需求返回原始头部或抛出异常
    }
}