<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="租户" prop="tenantId">
        <el-select v-model="queryParams.tenantId" placeholder="请选择租户" clearable size="small">
          <el-option v-for="item in tenantOptions" :key="item.tenantId" :label="item.tenantName" :value="item.tenantId" />
        </el-select>
      </el-form-item>
      <el-form-item label="配置名称" prop="configName">
        <el-input v-model="queryParams.configName" placeholder="请输入配置名称" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="配置键" prop="configKey">
        <el-input v-model="queryParams.configKey" placeholder="请输入配置键" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="配置类型" prop="configType">
        <el-select v-model="queryParams.configType" placeholder="请选择配置类型" clearable size="small">
          <el-option label="文本" value="text" />
          <el-option label="数字" value="number" />
          <el-option label="布尔" value="boolean" />
          <el-option label="选择" value="select" />
          <el-option label="图片" value="image" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:tenant:config:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['system:tenant:config:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:tenant:config:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-refresh" size="mini" :disabled="!currentTenantId" @click="handleReset" v-hasPermi="['system:tenant:config:edit']">重置默认</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleExport" v-hasPermi="['system:tenant:config:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="configList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="configId" width="100" />
      <el-table-column label="租户" align="center" prop="tenantId" width="100" :formatter="tenantFormat" />
      <el-table-column label="配置名称" align="center" prop="configName" :show-overflow-tooltip="true" />
      <el-table-column label="配置键" align="center" prop="configKey" :show-overflow-tooltip="true" />
      <el-table-column label="配置值" align="center" prop="configValue" :show-overflow-tooltip="true" :formatter="configValueFormat" />
      <el-table-column label="配置类型" align="center" prop="configType">
        <template slot-scope="scope">
          <dict-tag :options="configTypeOptions" :value="scope.row.configType"/>
        </template>
      </el-table-column>
      <el-table-column label="系统默认" align="center" prop="isDefault" width="80">
        <template slot-scope="scope">
          <dict-tag :options="yesNoOptions" :value="scope.row.isDefault"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.statusBool"
            :active-value="true"
            :inactive-value="false"
            @change="handleStatusChange(scope.row)"
            active-color="#13ce66"
            inactive-color="#ff4949">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:tenant:config:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:tenant:config:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改租户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="租户" prop="tenantId">
          <el-select v-model="form.tenantId" placeholder="请选择租户" style="width: 100%">
            <el-option v-for="item in tenantOptions" :key="item.tenantId" :label="item.tenantName" :value="item.tenantId" />
          </el-select>
        </el-form-item>
        <el-form-item label="配置名称" prop="configName">
          <el-input v-model="form.configName" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="配置键" prop="configKey">
          <el-input v-model="form.configKey" placeholder="请输入配置键" />
        </el-form-item>
        <el-form-item label="配置类型" prop="configType">
          <el-select v-model="form.configType" placeholder="请选择配置类型" style="width: 100%">
            <el-option label="文本" value="text" />
            <el-option label="数字" value="number" />
            <el-option label="布尔" value="boolean" />
            <el-option label="选择" value="select" />
            <el-option label="图片" value="image" />
          </el-select>
        </el-form-item>
        <el-form-item label="配置值" prop="configValue">
          <el-input v-model="form.configValue" placeholder="请输入配置值" v-if="form.configType === 'text'" />
          <el-input-number v-model="form.configValue" :controls="true" style="width: 100%" v-else-if="form.configType === 'number'" />
          <el-select v-model="form.configValue" placeholder="请选择值" style="width: 100%" v-else-if="form.configType === 'boolean'">
            <el-option label="是" value="Y" />
            <el-option label="否" value="N" />
          </el-select>
          <el-select v-model="form.configValue" placeholder="请选择值" style="width: 100%" v-else-if="form.configType === 'select'">
            <el-option 
              v-for="(option, index) in JSON.parse(form.options || '[]')" 
              :key="index" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
          <el-upload
            v-else-if="form.configType === 'image'"
            class="avatar-uploader"
            action="/dev-api/common/upload"
            :headers="headers"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload">
            <img v-if="form.configValue" :src="form.configValue" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="配置描述" prop="configDesc">
          <el-input v-model="form.configDesc" type="textarea" placeholder="请输入配置描述" />
        </el-form-item>
        <el-form-item label="可选项" prop="options" v-if="form.configType === 'select'">
          <el-button type="primary" size="mini" @click="addOption">添加选项</el-button>
          <div v-for="(option, index) in optionList" :key="index" style="margin-top: 10px; display: flex; align-items: center;">
            <el-input v-model="option.label" placeholder="标签" style="width: 40%; margin-right: 10px;" />
            <el-input v-model="option.value" placeholder="值" style="width: 40%; margin-right: 10px;" />
            <el-button type="danger" size="mini" icon="el-icon-delete" @click="removeOption(index)"></el-button>
          </div>
        </el-form-item>
        <el-form-item label="显示顺序" prop="orderNum">
          <el-input-number v-model="form.orderNum" :min="0" :max="999" controls-position="right" />
        </el-form-item>
        <el-form-item label="是否默认" prop="isDefault">
          <el-radio-group v-model="form.isDefault">
            <el-radio label="Y">是</el-radio>
            <el-radio label="N">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTenantConfig, getTenantConfig, getConfigByTenantId, addTenantConfig, updateTenantConfig, delTenantConfig, resetToDefault } from "@/api/system/tenant/config";
import { getToken } from "@/utils/auth";
import { listTenant} from "@/api/system/tenant/tenant"
export default {
  name: "TenantConfig",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 租户配置表格数据
      configList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前选中的租户ID
      currentTenantId: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: null,
        configKey: null,
        configName: null,
        configType: null,
        status: null
      },
      // 表单参数
      form: {},
      // 可选项列表
      optionList: [],
      // 租户选项
      tenantOptions: [],
      // 配置类型选项
      configTypeOptions: [
        { dictLabel: "文本", dictValue: "text" },
        { dictLabel: "数字", dictValue: "number" },
        { dictLabel: "布尔", dictValue: "boolean" },
        { dictLabel: "选择", dictValue: "select" },
        { dictLabel: "图片", dictValue: "image" }
      ],
      // 状态选项
      statusOptions: [
        { dictLabel: "正常", dictValue: "0" },
        { dictLabel: "停用", dictValue: "1" }
      ],
      // 是否选项
      yesNoOptions: [
        { dictLabel: "是", dictValue: "Y" },
        { dictLabel: "否", dictValue: "N" }
      ],
      // 上传头部
      headers: {
        Authorization: "Bearer " + getToken()
      },
      // 表单校验
      rules: {
        tenantId: [
          { required: true, message: "租户ID不能为空", trigger: "blur" }
        ],
        configName: [
          { required: true, message: "配置名称不能为空", trigger: "blur" }
        ],
        configKey: [
          { required: true, message: "配置键不能为空", trigger: "blur" }
        ],
        configType: [
          { required: true, message: "配置类型不能为空", trigger: "change" }
        ],
        configValue: [
          { required: true, message: "配置值不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getTenantOptions();
  },
  methods: {
    /** 查询租户配置列表 */
    getList() {
      this.loading = true;
      listTenantConfig(this.queryParams).then(response => {
        // 为每行数据添加statusBool属性，用于el-switch组件绑定
        this.configList = response.rows.map(row => {
          return {
            ...row,
            statusBool: row.status === '0'
          }
        });
        this.total = response.total;
        this.loading = false;
      });
    },
    // 获取租户选项
    getTenantOptions() {
      // 模拟租户数据，实际应该从API获取
      listTenant({
        pageNum: 1,
        pageSize: 1000
      }).then(response => {
        this.tenantOptions = response.rows;
      });
    },
    // 租户格式化
    tenantFormat(row) {
      for (let i = 0; i < this.tenantOptions.length; i++) {
        if (this.tenantOptions[i].tenantId === row.tenantId) {
          return this.tenantOptions[i].tenantName;
        }
      }
      return row.tenantId;
    },
    // 配置值格式化
    configValueFormat(row) {
      if (row.configType === 'boolean') {
        return row.configValue === 'Y' ? '是' : '否';
      }
      return row.configValue;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        configId: null,
        tenantId: null,
        configKey: null,
        configValue: null,
        configType: "text",
        isDefault: "N",
        configName: null,
        configDesc: null,
        options: "[]",
        orderNum: 0,
        status: "0",
        remark: null
      };
      this.optionList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.configId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
      
      // 获取当前选中的租户ID
      if (selection.length > 0) {
        this.currentTenantId = selection[0].tenantId;
      } else {
        this.currentTenantId = null;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加租户配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const configId = row.configId || this.ids[0];
      getTenantConfig(configId).then(response => {
        this.form = response.data;
        if (this.form.configType === 'select' && this.form.options) {
          try {
            this.optionList = JSON.parse(this.form.options);
          } catch (error) {
            this.optionList = [];
          }
        }
        this.open = true;
        this.title = "修改租户配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 如果是select类型，更新options字段
          if (this.form.configType === 'select') {
            this.form.options = JSON.stringify(this.optionList);
          }
          
          if (this.form.configId != null) {
            updateTenantConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTenantConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const configIds = row.configId || this.ids;
      this.$modal.confirm('是否确认删除租户配置编号为"' + configIds + '"的数据项？').then(function() {
        return delTenantConfig(configIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 重置默认配置 */
    handleReset() {
      if (!this.currentTenantId) {
        this.$modal.msgError("请先选择一个租户");
        return;
      }
      
      this.$modal.confirm('是否确认将租户ID为"' + this.currentTenantId + '"的配置重置为默认值？').then(function() {
        return resetToDefault(this.currentTenantId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("重置成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/tenant/config/export', {
        ...this.queryParams
      }, `tenant_config_${new Date().getTime()}.xlsx`);
    },
    // 添加选项
    addOption() {
      this.optionList.push({ label: '', value: '' });
    },
    // 删除选项
    removeOption(index) {
      this.optionList.splice(index, 1);
    },
    // 图片上传成功回调
    handleImageSuccess(res, file) {
      if (res.code === 200) {
        this.form.configValue = res.url;
      } else {
        this.$modal.msgError("上传图片失败");
      }
    },
    // 图片上传前的校验
    beforeImageUpload(file) {
      const isImage = file.type.indexOf('image') !== -1;
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$modal.msgError('上传图片只能是图片格式!');
        return false;
      }
      if (!isLt2M) {
        this.$modal.msgError('上传图片大小不能超过 2MB!');
        return false;
      }
      return true;
    },
    // 修改状态
    handleStatusChange(row) {
      let text = row.statusBool ? "启用" : "停用";
      this.$modal.confirm('确认要' + text + '"' + row.configName + '"的配置吗？').then(function() {
        return updateTenantConfig({
          configId: row.configId,
          status: row.statusBool ? '0' : '1'
        });
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(() => {
        row.statusBool = !row.statusBool;
      });
    }
  }
};
</script>

<style scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
