<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.ImGroupMemberMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.ImGroupMember">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="group_id" property="groupId" />
        <result column="user_id" property="userId" />
        <result column="nickname" property="nickname" />
        <result column="role" property="role" />
        <result column="join_time" property="joinTime" />
        <result column="mute_end_time" property="muteEndTime" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, group_id, user_id, nickname, role, join_time, mute_end_time, status, create_time, update_time
    </sql>

</mapper>
