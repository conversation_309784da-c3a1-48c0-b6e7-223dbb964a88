package com.ruoyi.web.controller.app;

import com.ruoyi.app.domain.ImYxaccountMapping;
import com.ruoyi.app.domain.QwxTenantConfig;
import com.ruoyi.app.domain.QwxUserTenant;
import com.ruoyi.app.model.dto.UpdataUserInfoDto;
import com.ruoyi.app.model.dto.UpdateOnlineStatusDto;
import com.ruoyi.app.model.vo.AppUserTenantIMVo;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.app.model.dto.JoinTenantDto;
import com.ruoyi.app.model.vo.AppLoginResultVo;
import com.ruoyi.app.model.vo.UserJoinTenantInfoList;
import com.ruoyi.web.service.TenantConfigService;
import com.ruoyi.web.utils.AppContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 租户组织配置
 *
 * <AUTHOR>
 * @date 2025/5/13 下午3:44
 */
@Api(tags = "租户组织相关接口")
@RestController
@RequestMapping("/app/tenant")
public class AppTenantController {

    @Resource
    private TenantConfigService tenantConfigService;

    /**
     * 获取租户配置
     *
     * @return 租户配置
     */
    @ApiOperation("获取当前组织配置项信息列表")
    @GetMapping("/getTenantConfig")
    public R<List<QwxTenantConfig>> getTenantConfig() {
        return R.ok(tenantConfigService.getTenantConfig());
    }



    @PostMapping("/updateUserInfo")
    @ApiOperation("修改组织的用户资料")
    public AjaxResult updateUserInfo(@RequestBody UpdataUserInfoDto updataUserInfoDto) {
        return tenantConfigService.updateUserInfo(updataUserInfoDto);
    }

    /**
     * 查询组织内用户列表
     *
     * @return 用户列表
     */
    @GetMapping("/listUsers")
    @ApiOperation("查询组织内用户列表")
    public R<List<QwxUserTenant>> listUsers() {
        return tenantConfigService.listUsers(AppContext.getCurrentTenantId());
    }

    /**
     * 退出当前组织
     *
     * @return 操作结果
     */
    @PostMapping("/quitTenant")
    @ApiOperation("退出当前组织")
    public AjaxResult quitTenant() {
        return tenantConfigService.quitTenant();
    }

    /**
     * 获取用户资料
     *
     * @return 用户资料
     */
    @GetMapping("/getUserInfo")
    @ApiOperation("获取用户资料")
    public R<QwxUserTenant> getUserInfo(@RequestParam(required = false) Long tenantUserId) {
        if (ObjectUtils.isEmpty(tenantUserId)) {
          tenantUserId = AppContext.getCurrentTenantUserId();
        }
       return  tenantConfigService.getUserInfo(tenantUserId);
    }

    /**
     * 修改用户在线状态
     *
     * @param updateOnlineStatusDto 在线状态DTO
     * @return 操作结果
     */
    @PostMapping("/updateOnlineStatus")
    @ApiOperation(value = "修改在线状态", notes = "修改用户在当前组织中的在线状态")
    public AjaxResult updateOnlineStatus(@RequestBody @Validated UpdateOnlineStatusDto updateOnlineStatusDto) {
        return tenantConfigService.updateOnlineStatus(updateOnlineStatusDto);
    }
}
