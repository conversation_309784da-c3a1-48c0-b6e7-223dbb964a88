<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.ImYxaccountMappingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.ImYxaccountMapping">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="global_user_id" property="globalUserId" />
        <result column="tenant_id" property="tenantId" />
        <result column="yx_account_id" property="yxAccountId" />
        <result column="yx_token" property="yxToken" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, global_user_id, tenant_id, yx_account_id, yx_token, status, create_time, update_time
    </sql>
    
    <!-- 根据全局用户ID和租户ID查询云信账号信息 -->
    <select id="selectByGlobalUserIdAndTenantId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM im_yxaccount_mapping
        WHERE global_user_id = #{globalUserId}
        AND tenant_id = #{tenantId}
        AND status = '0'
        LIMIT 1
    </select>

</mapper>
