package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.Pattern;

/**
 * 用户登录入参
 * <AUTHOR>
 * @date 2025/5/10 下午11:32
 */
@Data
public class LoginDto {
    /**
     * 手机号
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @ApiModelProperty(value = "手机号", example = "13800138000")
    private String mobile;
    
    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    @ApiModelProperty(value = "邮箱地址", example = "<EMAIL>")
    private String email;
    
    /**
     * 验证码
     */
    @ApiModelProperty(value = "验证码", example = "1234")
    private String code;
    
    /**
     * 微信小程序code
     */
    @ApiModelProperty(value = "微信小程序登录code", example = "021abc123")
    private String wxCode;
    
    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型(ios/android/web)", example = "ios")
    private String deviceType;
    
    /**
     * 验证码类型（sms-短信验证码，email-邮箱验证码）
     */
    @ApiModelProperty(value = "验证码类型(sms/email)", example = "sms")
    private String codeType;
}
