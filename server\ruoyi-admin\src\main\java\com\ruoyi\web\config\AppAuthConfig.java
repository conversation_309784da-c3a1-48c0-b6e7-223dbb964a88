package com.ruoyi.web.config;

import com.ruoyi.web.interceptor.AppTokenInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * APP认证配置
 * <AUTHOR>
 * @date 2025/5/11 下午11:45
 */
@Configuration
public class AppAuthConfig implements WebMvcConfigurer {

    @Resource
    private AppTokenInterceptor appTokenInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加APP Token拦截器
        registry.addInterceptor(appTokenInterceptor)
                // 需要拦截的路径
                .addPathPatterns("/app/**")
                // 排除不需要拦截的路径
                .excludePathPatterns(
                        "/app/auth/login",
                        "/app/auth/sendCode",
                        "/app/tenant/joinTenant",
                        "/app/auth/refreshToken",
                        "/app/common/**",
                        "/app/yunxin/callback",
                        "/app/yunxin/copy"
                );
    }
} 