package com.ruoyi.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.app.domain.QwxTenantConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 租户配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
public interface QwxTenantConfigMapper extends BaseMapper<QwxTenantConfig> {
    /**
     * 根据租户ID查询租户配置
     * @param tenantId 租户ID
     * @return 租户配置
     */
    List<QwxTenantConfig> selectByTenantIdAndGlobalUserId(@Param("tenantId") Long tenantId);
}
