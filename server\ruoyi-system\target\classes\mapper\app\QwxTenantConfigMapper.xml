<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.QwxTenantConfigMapper">
    
    <resultMap type="com.ruoyi.app.domain.QwxTenantConfig" id="QwxTenantConfigResult">
        <id     property="configId"      column="config_id"      />
        <result property="tenantId"      column="tenant_id"      />
        <result property="configKey"     column="config_key"     />
        <result property="configValue"   column="config_value"   />
        <result property="configType"    column="config_type"    />
        <result property="isDefault"     column="is_default"     />
        <result property="configName"    column="config_name"    />
        <result property="configDesc"    column="config_desc"    />
        <result property="options"       column="options"        />
        <result property="orderNum"      column="order_num"      />
        <result property="status"        column="status"         />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

  <!-- 根据租户ID和全局用户ID查询租户配置 -->
  <select id="selectByTenantIdAndGlobalUserId" resultMap="QwxTenantConfigResult">
    select * from qwx_tenant_config where tenant_id = #{tenantId}
  </select>    
</mapper> 