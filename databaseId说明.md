# 容易混淆的ID说明解释

1. **全局用户ID (`global_user_id`)**  
   - 类型：`varchar(64)`  
   - 作用：唯一标识一个用户，跨租户使用。例如，一个用户可能在多个租户（企业/组织）中拥有不同的角色或权限，但全局用户ID是唯一的。  
   - 示例：`sys_global_user` 表中的主键。

2. **租户ID (`tenant_id`)**  
   - 类型：`bigint(20)`  
   - 作用：标识一个租户（企业/组织）。每个租户有独立的配置、用户群和数据隔离。  
   - 示例：`sys_tenant` 表中的主键。

3. **租户内用户ID (`user_id`)**  
   - 类型：`bigint(20)`  
   - 作用：在某个租户内部唯一标识一个用户。同一个全局用户在不同租户中可能有不同的 `user_id`。  
   - 示例：`sys_user_tenant` 表中的字段，用于关联租户内的用户。

4. **ID (`id`)**  
   - 类型：`bigint(20)`  
   - 作用：通常作为表的主键，自增生成，用于唯一标识一条记录。  
   - 示例：`sys_user_tenant` 表中的主键。


### 总结
- **全局用户ID**：跨租户唯一标识用户。  
- **租户ID**：标识企业/组织。  
- **租户内用户ID**：租户内部唯一标识用户。  
- **其他ID**：通常是表的主键，用于唯一标识记录。  

