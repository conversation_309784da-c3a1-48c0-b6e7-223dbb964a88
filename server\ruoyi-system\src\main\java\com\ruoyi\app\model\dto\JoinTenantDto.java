package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 用户加入租户DTO
 * <AUTHOR>
 * @date 2025/5/30
 */
@Data
@ApiModel("用户加入租户DTO")
public class JoinTenantDto {

    /**
     * 邀请码
     */
    @NotBlank(message = "邀请码不能为空")
    @ApiModelProperty(value = "邀请码", required = true)
    private String inviteCode;
} 