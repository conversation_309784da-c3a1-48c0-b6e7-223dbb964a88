package com.ruoyi.web.service;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.app.domain.ImYxaccountMapping;
import com.ruoyi.app.domain.QwxGlobalUser;
import com.ruoyi.app.domain.QwxTenant;
import com.ruoyi.app.domain.QwxUserTenant;
import com.ruoyi.app.mapper.QwxTenantMapper;
import com.ruoyi.app.service.ImYxaccountMappingService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.web.utils.NestEastUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.util.Date;
import java.util.UUID;

/**
 * 网易云信服务层
 *
 * <AUTHOR>
 * @date 2025/5/13 下午5:10
 */
@Slf4j
@Service
public class WyImService {

    // 网易云信API配置
    @Value("${yunxin.api-url}")
    private String YX_API_URL;
    @Value("${yunxin.app-key}")
    private String YX_APP_KEY;
    @Value("${yunxin.app-secret}")
    private String YX_APP_SECRET;
    @Resource
    private ImYxaccountMappingService imYxaccountMappingService;
    @Resource
    private QwxTenantMapper qwxTenantMapper;

    /**
     * 创建网易云信IM账号
     *
     * @param globalUser 全局用户信息
     * @param userTenant 用户租户关联信息
     * @return 是否创建成功
     */
    public ImYxaccountMapping createYunxinIMAccount(QwxGlobalUser globalUser, QwxUserTenant userTenant) throws Exception {
        // 1. 检查是否已经创建过云信账号
        ImYxaccountMapping existMapping = imYxaccountMappingService.lambdaQuery()
                .eq(ImYxaccountMapping::getGlobalUserId, globalUser.getGlobalUserId())
                .eq(ImYxaccountMapping::getTenantId, userTenant.getTenantId())
                .eq(ImYxaccountMapping::getUserId, userTenant.getUserId())
                .eq(ImYxaccountMapping::getStatus, "0")
                .last("LIMIT 1")
                .one();

        if (existMapping != null) {
            log.info("用户在该租户下已存在云信账号映射: globalUserId={}, tenantId={}, yxAccountId={}",
                    globalUser.getGlobalUserId(), userTenant.getTenantId(), existMapping.getYxAccountId());
            return existMapping;
        }

        // 2. 生成请求参数
        // 为每个租户创建单独的云信账号ID：使用globalUserId_tenantId格式确保唯一性
        String accid = userTenant.getUserId() + "_" + userTenant.getTenantId();
        accid = accid.length() > 32 ? accid.substring(0, 32) : accid; // 云信ID最大32位

        // 生成请求参数
        String nonce = UUID.randomUUID()
                .toString()
                .replaceAll("-", "")
                .substring(0, 10);
        String curTime = String.valueOf(System.currentTimeMillis() / 1000);
        String checksum = NestEastUtils.getCheckSum(YX_APP_SECRET, nonce, curTime);
        // 构建请求体
        StringBuilder paramBuilder = new StringBuilder();
        paramBuilder.append("accid=")
                .append(URLEncoder.encode(accid, "UTF-8"));

        // 设置用户昵称 - 添加租户信息以区分不同租户下的同一用户
        String displayName = userTenant.getNickName();
        if (StringUtils.isNotEmpty(displayName)) {
            // 可以考虑附加租户名称到昵称，如果有租户名称信息
            QwxTenant tenant = qwxTenantMapper.selectById(userTenant.getTenantId());
            if (tenant != null && StringUtils.isNotEmpty(tenant.getTenantName())) {
                displayName = displayName + "@" + tenant.getTenantName();
            }
            paramBuilder.append("&name=")
                    .append(URLEncoder.encode(displayName, "UTF-8"));
        }

        // 添加头像如果存在
        if (StringUtils.isNotEmpty(userTenant.getAvatar())) {
            paramBuilder.append("&icon=")
                    .append(URLEncoder.encode(userTenant.getAvatar(), "UTF-8"));
        }

        // 3. 发送请求
        // 设置请求头
        String url = YX_API_URL;
        String param = paramBuilder.toString();

        // 调用HTTP工具发送请求
        String response = sendPostWithAuth(url, param, YX_APP_KEY, nonce, curTime, checksum);
        log.info("云信账号创建响应: {}", response);

        // 4. 解析响应
        JSONObject jsonResponse = JSONObject.parseObject(response);
        int code = jsonResponse.getIntValue("code");

        if (code == 200) {
            // 创建成功
            String token = jsonResponse.getJSONObject("info").getString("token");
            log.info("云信账号创建成功: accid={}, token={}", accid, token);

            // 5. 保存到映射表
            ImYxaccountMapping mapping = new ImYxaccountMapping();
            mapping.setGlobalUserId(globalUser.getGlobalUserId());
            mapping.setTenantId(userTenant.getTenantId());
            mapping.setUserId(userTenant.getUserId());
            mapping.setYxAccountId(accid);
            mapping.setYxToken(token);
            mapping.setStatus("0"); // 正常状态
            mapping.setCreateTime(new Date());

            // 保存映射关系
            imYxaccountMappingService.save(mapping);

            return mapping;
        } else {
            // 创建失败
            String msg = jsonResponse.getString("desc");
            log.error("云信账号创建失败: code={}, msg={}", code, msg);
            throw new ServiceException("云信账号创建失败: " + msg);
        }

    }



    /**
     * 发送带认证头的POST请求
     *
     * @param url      url
     * @param param    参数
     * @param appKey   应用关键
     * @param nonce    随机数
     * @param curTime  当前 UTC 时间戳
     * @param checkSum 校验和
     * @return {@link String }
     */
    private String sendPostWithAuth(String url, String param, String appKey, String nonce, String curTime, String checkSum) {
        try {
            log.info("发送请求: url={}, param={}", url, param);
            return HttpUtil.createPost(url)
                    .header("Content-Type", "application/x-www-form-urlencoded;charset=utf-8")
                    .header("AppKey", appKey)
                    .header("Nonce", nonce)
                    .header("CurTime", curTime)
                    .header("CheckSum", checkSum)
                    .body(param)
                    .execute()
                    .body();
        } catch (Exception e) {
            log.error("发送请求异常", e);
            return "";
        }
    }
}
