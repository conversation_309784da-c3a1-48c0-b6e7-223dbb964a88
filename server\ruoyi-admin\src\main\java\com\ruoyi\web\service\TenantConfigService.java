package com.ruoyi.web.service;

import com.ruoyi.app.domain.*;
import com.ruoyi.app.mapper.QwxTenantConfigMapper;
import com.ruoyi.app.mapper.QwxTenantMapper;
import com.ruoyi.app.model.dto.JoinTenantDto;
import com.ruoyi.app.model.dto.UpdataUserInfoDto;
import com.ruoyi.app.model.dto.UpdateOnlineStatusDto;
import com.ruoyi.app.model.vo.AppUserTenantIMVo;
import com.ruoyi.app.service.ImYxaccountMappingService;
import com.ruoyi.app.service.QwxGlobalUserService;
import com.ruoyi.app.service.QwxTenantInviteCodeService;
import com.ruoyi.app.service.QwxUserTenantService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.web.utils.AppContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 组织配置业务层
 *
 * <AUTHOR>
 * @date 2025/5/13 下午3:49
 */
@Slf4j
@Service
public class TenantConfigService {

    @Resource
    private QwxTenantConfigMapper qwxTenantConfigMapper;
    @Resource
    private QwxGlobalUserService qwxGlobalUserService;
    @Resource
    private QwxTenantInviteCodeService qwxTenantInviteCodeService;
    @Resource
    private QwxUserTenantService qwxUserTenantService;
    @Resource
    private WyImService wyImService;
    @Resource
    private QwxTenantMapper qwxTenantMapper;
    @Resource
    private AppUserTokenService appUserTokenService;
    @Resource
    private ImYxaccountMappingService imYxaccountMappingService;

    /**
     * 获取租户配置
     *
     * @return 租户配置列表
     */
    public List<QwxTenantConfig> getTenantConfig() {
        return qwxTenantConfigMapper.selectByTenantIdAndGlobalUserId(AppContext.getCurrentTenantId());
    }

    /**
     * 通过邀请码加入租户
     *
     * @param joinTenantDto 加入租户DTO
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public R<AppUserTenantIMVo.TenantIMInfo> joinTenantByInviteCode(JoinTenantDto joinTenantDto) {
        final QwxGlobalUser globalUser = AppContext.getAppLoginUser();


        String loginAccount = StringUtils.isNotEmpty(globalUser.getPhone()) ? globalUser.getPhone() : globalUser.getEmail();
        try {
            // 1. 获取用户信息


            // 2. 查询邀请码是否有效
            QwxTenantInviteCode inviteCode = qwxTenantInviteCodeService.lambdaQuery()
                    .eq(QwxTenantInviteCode::getInviteCode, joinTenantDto.getInviteCode())
                    .eq(QwxTenantInviteCode::getStatus, "0") // 状态为有效
                    .last("LIMIT 1")
                    .one();

            if (inviteCode == null) {
                return R.fail("邀请码无效或已过期");
            }

            // 3. 检查邀请码使用次数限制
            if (inviteCode.getMaxUses() > 0 && inviteCode.getUsedCount() >= inviteCode.getMaxUses()) {
                return R.fail("邀请码已达到最大使用次数");
            }

            // 4. 检查过期时间
            if (inviteCode.getExpireTime() != null && inviteCode.getExpireTime()
                    .before(new Date())) {
                return R.fail("邀请码已过期");
            }

            // 5. 检查用户是否已经关联了该租户
            QwxUserTenant existUserTenant = qwxUserTenantService.lambdaQuery()
                    .eq(QwxUserTenant::getGlobalUserId, globalUser.getGlobalUserId())
                    .eq(QwxUserTenant::getTenantId, inviteCode.getTenantId())
                    .eq(QwxUserTenant::getIsQuit, "0")
                    .last("LIMIT 1")
                    .one();

            if (existUserTenant != null) {
                return R.fail("您已经加入该组织");
            }

            // 6. 查询当前租户已经有多少用户
            long defaultTenantId = 10000L; // 默认租户ID
            long tenantUserCount = qwxUserTenantService.lambdaQuery()
                    .eq(QwxUserTenant::getTenantId, inviteCode.getTenantId())
                    .count();

            // 7. 创建用户租户关系
            QwxUserTenant userTenant = new QwxUserTenant();
            userTenant.setGlobalUserId(globalUser.getGlobalUserId());
            userTenant.setTenantId(inviteCode.getTenantId());
            userTenant.setNickName("用户" + loginAccount.substring(Math.max(0, loginAccount.length() - 4)));
            userTenant.setJoinTime(new Date());
            userTenant.setStatus("0"); // 正常状态
            userTenant.setUserId(defaultTenantId + tenantUserCount + 1L); // 设置租户内用户ID为当前租户用户数量+1

            // 8. 根据邀请码设置是否需要审核
            if ("1".equals(inviteCode.getNeedApproval())) {
                userTenant.setStatus("2"); // 待审核状态
            }

            userTenant.setOnlineStatus("0"); // 离线状态
            userTenant.setCreateTime(new Date());

            // 9. 保存用户租户关系
            qwxUserTenantService.save(userTenant);

            // 10. 更新邀请码使用次数
            inviteCode.setUsedCount(inviteCode.getUsedCount() + 1);
            qwxTenantInviteCodeService.updateById(inviteCode);

            // 11. 关联网易云信账户，创建IM账号
            wyImService.createYunxinIMAccount(globalUser, userTenant);

            // 12. 生成对应的token信息并返回
            if ("1".equals(inviteCode.getNeedApproval())) {
                return R.ok(null, "申请已提交，请等待管理员审核");
            }

            AppUserTenantIMVo.TenantIMInfo tenantInfo = appUserTokenService.generateSingleTenantLoginInfo(
                    globalUser.getGlobalUserId(),
                    inviteCode.getTenantId()
                            .toString()); // 转换为String类型

            return R.ok(tenantInfo);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 更新用户信息
     *
     * @param updataUserInfoDto 更新用户信息DTO
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateUserInfo(UpdataUserInfoDto updataUserInfoDto) {
        final String globalUserId = AppContext.getGlobalUserId();
        if (StringUtils.isEmpty(globalUserId)) {
            return AjaxResult.error("全局用户ID不能为空");
        }

        try {
            // 1. 获取用户信息
            QwxGlobalUser globalUser = qwxGlobalUserService.lambdaQuery()
                    .eq(QwxGlobalUser::getGlobalUserId, globalUserId)
                    .eq(QwxGlobalUser::getDelFlag, "0")
                    .last("LIMIT 1")
                    .one();

            if (globalUser == null) {
                return AjaxResult.error("用户不存在");
            }

            // 2. 更新用户基本信息
            globalUser.setSex(updataUserInfoDto.getSex());
            globalUser.setUpdateTime(new Date());
            qwxGlobalUserService.updateById(globalUser);


            // 3. 更新用户租户信息
            // 优先使用请求中的租户ID，如果没有则使用当前上下文的租户ID
            Long tenantId = AppContext.getCurrentTenantId();

            if (tenantId == null) {
                return AjaxResult.error("租户ID不能为空");
            }

            QwxUserTenant userTenant = qwxUserTenantService.lambdaQuery()
                    .eq(QwxUserTenant::getGlobalUserId, globalUserId)
                    .eq(QwxUserTenant::getTenantId, tenantId)
                    .last("LIMIT 1")
                    .one();

            if (userTenant == null) {
                return AjaxResult.error("您不属于该组织");
            }

            userTenant.setNickName(updataUserInfoDto.getNickName());
            userTenant.setAvatar(updataUserInfoDto.getAvatar());
            userTenant.setUpdateTime(new Date());
            qwxUserTenantService.updateById(userTenant);

            return AjaxResult.success("个人资料更新成功");
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 获取当前用户在当前租户中的信息
     *
     * @return 用户租户关系信息，如果未找到返回null
     */
    public QwxUserTenant getCurrentUserTenantInfo() {
        // 从上下文获取全局用户ID和当前租户ID
        String globalUserId = AppContext.getGlobalUserId();
        Long tenantId = AppContext.getCurrentTenantId();

        if (StringUtils.isEmpty(globalUserId) || tenantId == null) {
            return null;
        }

        // 查询用户在当前租户中的信息
        return qwxUserTenantService.lambdaQuery()
                .eq(QwxUserTenant::getGlobalUserId, globalUserId)
                .eq(QwxUserTenant::getTenantId, tenantId)
                .last("LIMIT 1")
                .one();
    }

    /**
     * 检查当前用户是否是租户管理员
     *
     * @return 是否为管理员
     */
    public boolean isCurrentUserTenantAdmin() {
        QwxUserTenant userTenant = getCurrentUserTenantInfo();
        // 根据业务规则判断是否是管理员，这里暂时假设userId为1的是管理员
        return userTenant != null && userTenant.getUserId() == 1L;
    }

    /**
     * 查询组织内用户列表
     *
     * @param tenantId 租户ID
     * @return 用户列表
     */
    public R<List<QwxUserTenant>> listUsers(Long tenantId) {
        if (ObjectUtils.isEmpty(tenantId)) {
            return R.fail("组织ID不能为空");
        }

        List<QwxUserTenant> userList = qwxUserTenantService.lambdaQuery()
                .eq(QwxUserTenant::getTenantId, tenantId)
                .eq(QwxUserTenant::getIsQuit, "0")
                .orderByDesc(QwxUserTenant::getIsAdmin)
                .list();

        return R.ok(userList);
    }

    public AjaxResult quitTenant() {
        // 获取当前用户和租户信息
        String globalUserId = AppContext.getGlobalUserId();
        Long tenantId = AppContext.getCurrentTenantId();

        if (StringUtils.isEmpty(globalUserId) || tenantId == null) {
            return AjaxResult.error("用户未登录或未加入组织");
        }

        // 更新用户租户关系状态为已退出
        boolean updateResult = qwxUserTenantService.lambdaUpdate()
                .eq(QwxUserTenant::getGlobalUserId, globalUserId)
                .eq(QwxUserTenant::getTenantId, tenantId)
                .set(QwxUserTenant::getIsQuit, "1")
                .update();

        if (!updateResult) {
            return AjaxResult.error("退出组织失败");
        }
        imYxaccountMappingService.lambdaUpdate()
                .eq(ImYxaccountMapping::getGlobalUserId, globalUserId)
                .eq(ImYxaccountMapping::getUserId, AppContext.getCurrentTenantUserId())
                .eq(ImYxaccountMapping::getTenantId, tenantId)
                .set(ImYxaccountMapping::getStatus, "1")
                .set(ImYxaccountMapping::getUpdateTime, new Date())
                .update();

        return AjaxResult.success("退出组织成功");
    }

    /**
     * 获取用户资料
     *
     * @param tenantUserId 组织内用户ID
     * @return 用户资料
     */
    public R<QwxUserTenant> getUserInfo(Long tenantUserId) {
        try {

            QwxUserTenant userTenant = qwxUserTenantService.lambdaQuery()
                    .eq(QwxUserTenant::getUserId, tenantUserId)
                    .eq(QwxUserTenant::getTenantId, AppContext.getCurrentTenantId())
                    .eq(QwxUserTenant::getIsQuit, "0")
                    .one();

            return R.ok(userTenant);

        } catch (Exception e) {
            log.error("获取用户资料异常", e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 更新用户在线状态
     *
     * @param updateOnlineStatusDto 更新在线状态DTO
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateOnlineStatus(UpdateOnlineStatusDto updateOnlineStatusDto) {
        try {
            // 获取用户ID，如果DTO中未指定，则使用当前登录用户
            String globalUserId = AppContext.getGlobalUserId();

            if (StringUtils.isEmpty(globalUserId)) {
                return AjaxResult.error("用户ID不能为空");
            }

            // 获取当前租户ID
            Long tenantId = AppContext.getCurrentTenantId();
            if (tenantId == null) {
                return AjaxResult.error("组织ID不能为空");
            }

            // 查询用户租户关系
            QwxUserTenant userTenant = qwxUserTenantService.lambdaQuery()
                    .eq(QwxUserTenant::getGlobalUserId, globalUserId)
                    .eq(QwxUserTenant::getTenantId, tenantId)
                    .eq(QwxUserTenant::getIsQuit, "0")
                    .last("LIMIT 1")
                    .one();

            if (userTenant == null) {
                return AjaxResult.error("用户不属于该组织或已退出");
            }

            // 更新在线状态
            userTenant.setOnlineStatus(updateOnlineStatusDto.getOnlineStatus()
                    .toString());
            userTenant.setUpdateTime(new Date());

            boolean success = qwxUserTenantService.updateById(userTenant);
            if (!success) {
                return AjaxResult.error("更新在线状态失败");
            }

            return AjaxResult.success("更新在线状态成功");
        } catch (Exception e) {
            log.error("更新在线状态异常", e);
            throw new ServiceException(e.getMessage());
        }
    }
}
