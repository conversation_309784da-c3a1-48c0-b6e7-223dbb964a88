
# 微企信平台管理后台二期需求

- 至少26个接口业务功能需要实现

## 用户关系管理
1. 好友申请
   - 发送好友申请
   - 查询好友申请列表
   - 同意/拒绝添加好友
   - 确认添加好友

2. 好友管理
   - 添加好友
   - 删除好友
   - 更新好友信息
   - 查询好友信息
   - 分页查询好友列表

## 群组管理
1. 群组基础功能
   - 创建群组
   - 退出当前组织

2. 群组配置
   - 更新群组信息
   - 转让群主
   - 添加管理员
   - 移除管理员
   - 解散群组

3. 群组查询
   - 查询群组信息
   - 批量查询群组信息列表
   - 查询高级群在线成员列表
   - 批量查询高级群在线成员数
   - 分页查询群成员列表

4. 群成员管理
   - 拉人入群
   - 移除群成员
   - 设置群成员权限

## 接口设计

部分数据和信息需要从网易云信接口获取,功能实现通过第三方回调实现业务状态的同步功能,客户端只需要请求云信sdk即可,云信通过预设的回调地址,执行业务过来,服务端这边做通过还是拒绝的业务逻辑判断告知云信云端;

回调接口的具体业务功能需要通过MyBatisPlus实现CRUD操作，主要包括：
1. 用户好友关系表的增删改查
2. 好友申请记录表的增删改查
3. 群组信息表的增删改查
4. 群成员关系表的增删改查
5. 群组权限配置表的增删改查
