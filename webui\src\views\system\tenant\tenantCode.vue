<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="租户ID" prop="tenantId">
        <el-select v-model="queryParams.tenantId" placeholder="请选择租户" clearable size="small">
          <el-option v-for="item in tenantOptions" :key="item.tenantId" :label="item.tenantName" :value="item.tenantId" />
        </el-select>
      </el-form-item>
      <el-form-item label="邀请码" prop="inviteCode">
        <el-input
          v-model="queryParams.inviteCode"
          placeholder="请输入邀请码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="邀请码名称" prop="codeName">
        <el-input
          v-model="queryParams.codeName"
          placeholder="请输入邀请码名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建者ID" prop="creatorId">
        <el-input
          v-model="queryParams.creatorId"
          placeholder="请输入创建者ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最大使用次数" prop="maxUses">
        <el-input
          v-model="queryParams.maxUses"
          placeholder="请输入最大使用次数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="已使用次数" prop="usedCount">
        <el-input
          v-model="queryParams.usedCount"
          placeholder="请输入已使用次数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="过期时间" prop="expireTime">
        <el-date-picker clearable
          v-model="queryParams.expireTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择过期时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="是否自动激活账号" prop="autoActivate">
        <el-input
          v-model="queryParams.autoActivate"
          placeholder="请输入是否自动激活账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否需要审核" prop="needApproval">
        <el-input
          v-model="queryParams.needApproval"
          placeholder="请输入是否需要审核"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="默认角色" prop="defaultRole">
        <el-input
          v-model="queryParams.defaultRole"
          placeholder="请输入默认角色"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:code:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:code:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:code:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:code:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="codeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="邀请码ID" align="center" prop="codeId" />
      <el-table-column label="租户" align="center" prop="tenantId" width="100" :formatter="tenantFormat" />
      <el-table-column label="邀请码" align="center" prop="inviteCode" />
      <el-table-column label="邀请码名称" align="center" prop="codeName" />
      <el-table-column label="创建者ID" align="center" prop="creatorId" />
      <el-table-column label="最大使用次数" align="center" prop="maxUses" />
      <el-table-column label="已使用次数" align="center" prop="usedCount" />
      <el-table-column label="过期时间" align="center" prop="expireTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expireTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" />
      <el-table-column label="是否自动激活账号" align="center" prop="autoActivate" />
      <el-table-column label="是否需要审核" align="center" prop="needApproval" />
      <el-table-column label="默认角色" align="center" prop="defaultRole" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:code:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:code:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改租户邀请码对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="租户ID" prop="tenantId">
          <el-select v-model="form.tenantId" placeholder="请选择租户" style="width: 100%">
            <el-option v-for="item in tenantOptions" :key="item.tenantId" :label="item.tenantName" :value="item.tenantId" />
          </el-select>
        </el-form-item>
        <el-form-item label="邀请码" prop="inviteCode">
          <el-input v-model="form.inviteCode" placeholder="请输入邀请码" />
        </el-form-item>
        <el-form-item label="邀请码名称" prop="codeName">
          <el-input v-model="form.codeName" placeholder="请输入邀请码名称" />
        </el-form-item>
        <el-form-item label="创建者ID" prop="creatorId">
          <el-input v-model="form.creatorId" placeholder="请输入创建者ID" />
        </el-form-item>
        <el-form-item label="最大使用次数" prop="maxUses">
          <el-input v-model="form.maxUses" placeholder="请输入最大使用次数" />
        </el-form-item>
        <el-form-item label="已使用次数" prop="usedCount">
          <el-input v-model="form.usedCount" placeholder="请输入已使用次数" />
        </el-form-item>
        <el-form-item label="过期时间" prop="expireTime">
          <el-date-picker clearable
            v-model="form.expireTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择过期时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否自动激活账号" prop="autoActivate">
          <el-input v-model="form.autoActivate" placeholder="请输入是否自动激活账号" />
        </el-form-item>
        <el-form-item label="是否需要审核" prop="needApproval">
          <el-input v-model="form.needApproval" placeholder="请输入是否需要审核" />
        </el-form-item>
        <el-form-item label="默认角色" prop="defaultRole">
          <el-input v-model="form.defaultRole" placeholder="请输入默认角色" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCode, getCode, delCode, addCode, updateCode } from "@/api/system/tenant/tenantCode"
import { listTenant} from "@/api/system/tenant/tenant"
export default {
  name: "Code",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 租户邀请码表格数据
      codeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 租户选项
      tenantOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: null,
        inviteCode: null,
        codeName: null,
        creatorId: null,
        maxUses: null,
        usedCount: null,
        expireTime: null,
        status: null,
        autoActivate: null,
        needApproval: null,
        defaultRole: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        tenantId: [
          { required: true, message: "租户ID不能为空", trigger: "blur" }
        ],
        inviteCode: [
          { required: true, message: "邀请码不能为空", trigger: "blur" }
        ],
        creatorId: [
          { required: true, message: "创建者ID不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList()
    this.getTenantOptions()
  },
  methods: {
    /** 查询租户邀请码列表 */
    getList() {
      this.loading = true
      listCode(this.queryParams).then(response => {
        this.codeList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 获取租户选项
    getTenantOptions() {
      listTenant({
        pageNum: 1,
        pageSize: 1000
      }).then(response => {
        this.tenantOptions = response.rows;
      });
    },
    // 租户格式化
    tenantFormat(row) {
      for (let i = 0; i < this.tenantOptions.length; i++) {
        if (this.tenantOptions[i].tenantId === row.tenantId) {
          return this.tenantOptions[i].tenantName;
        }
      }
      return row.tenantId;
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        codeId: null,
        tenantId: null,
        inviteCode: null,
        codeName: null,
        creatorId: null,
        maxUses: null,
        usedCount: null,
        expireTime: null,
        status: null,
        autoActivate: null,
        needApproval: null,
        defaultRole: null,
        createTime: null,
        updateTime: null,
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.codeId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加租户邀请码"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const codeId = row.codeId || this.ids
      getCode(codeId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改租户邀请码"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.codeId != null) {
            updateCode(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addCode(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const codeIds = row.codeId || this.ids
      this.$modal.confirm('是否确认删除租户邀请码编号为"' + codeIds + '"的数据项？').then(function() {
        return delCode(codeIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/code/export', {
        ...this.queryParams
      }, `code_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
