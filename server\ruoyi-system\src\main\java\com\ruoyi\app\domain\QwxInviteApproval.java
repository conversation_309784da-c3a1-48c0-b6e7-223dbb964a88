package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 邀请申请审核表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QwxInviteApproval implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 审核ID
     */
    @TableId(value = "approval_id", type = IdType.AUTO)
    private Long approvalId;

    /**
     * 邀请码使用记录ID
     */
    private Long usageId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 申请用户ID
     */
    private Long userId;

    /**
     * 全局用户ID
     */
    private String globalUserId;

    /**
     * 审核人ID
     */
    private Long approverId;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 审核时间
     */
    private Date approvalTime;

    /**
     * 审核状态（0待审核 1已通过 2已拒绝）
     */
    private String approvalStatus;

    /**
     * 拒绝原因
     */
    private String rejectionReason;

    /**
     * 申请信息（用户填写）
     */
    private String applyInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
