import request from '@/utils/request'

// 查询租户邀请码列表
export function listCode(query) {
  return request({
    url: '/system/code/list',
    method: 'get',
    params: query
  })
}

// 查询租户邀请码详细
export function getCode(codeId) {
  return request({
    url: '/system/code/' + codeId,
    method: 'get'
  })
}

// 新增租户邀请码
export function addCode(data) {
  return request({
    url: '/system/code',
    method: 'post',
    data: data
  })
}

// 修改租户邀请码
export function updateCode(data) {
  return request({
    url: '/system/code',
    method: 'put',
    data: data
  })
}

// 删除租户邀请码
export function delCode(codeId) {
  return request({
    url: '/system/code/' + codeId,
    method: 'delete'
  })
}
