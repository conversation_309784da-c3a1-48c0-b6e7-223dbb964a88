<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.QwxUserTenantMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.QwxUserTenant">
        <id column="id" property="id" />
        <result column="global_user_id" property="globalUserId" />
        <result column="tenant_id" property="tenantId" />
        <result column="user_id" property="userId" />
        <result column="nick_name" property="nickName" />
        <result column="position" property="position" />
        <result column="join_time" property="joinTime" />
        <result column="status" property="status" />
        <result column="online_status" property="onlineStatus" />
        <result column="is_default" property="isDefault" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 租户和云信账号信息结果映射 -->
    <resultMap id="TenantIMInfoResultMap" type="com.ruoyi.app.model.vo.AppUserTenantIMVo$TenantIMInfo">
        <!-- 租户信息 -->
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="tenant_code" property="tenantCode" />
        <result column="logo" property="logo" />
        <result column="industry" property="industry" />
        <result column="scale" property="scale" />
        <result column="address" property="address" />
        <result column="contact_user" property="contactUser" />
        <result column="contact_phone" property="contactPhone" />
        <result column="domain" property="domain" />
        <result column="intro" property="intro" />
        <result column="status" property="tenantStatus" />
        <result column="expire_time" property="expireTime" />
        <result column="account_count" property="accountCount" />

        <!-- 用户信息 -->
        <result column="is_admin" property="isAdmin" />
        <result column="nick_name" property="nickName" />
        <result column="avatar" property="avatar" />
        <result column="position" property="position" />
        <result column="join_time" property="joinTime" />
        <result column="is_quit" property="isQuit" />
        
        <!-- 云信账号信息 -->
        <result column="im_id" property="imMappingId" />
        <result column="im_user_id" property="userId" />
        <result column="im_global_user_id" property="globalUserId" />
        <result column="yx_account_id" property="yxAccountId" />
        <result column="yx_token" property="yxToken" />
        <result column="im_status" property="imStatus" />
        <result column="im_create_time" property="createTime" />
        <result column="im_update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, global_user_id, tenant_id, user_id, nick_name, position, join_time, status, online_status, is_default, create_time, update_time
    </sql>
    <select id="selectListByGlobalUserId" resultType="com.ruoyi.app.model.vo.UserJoinTenantInfoList">
        SELECT 
            ut.id, 
            ut.global_user_id, 
            ut.tenant_id, 
            ut.user_id, 
            ut.nick_name, 
            ut.avatar,
            ut.position, 
            ut.join_time, 
            ut.status, 
            ut.online_status, 
            ut.is_default, 
            ut.create_time, 
            ut.update_time,
            t.tenant_name,
            t.tenant_code,
            t.logo
        FROM 
            qwx_user_tenant ut
        LEFT JOIN 
            qwx_tenant t ON ut.tenant_id = t.tenant_id
        WHERE 
            ut.global_user_id = #{globalUserId}
            AND ut.status = '0'
            AND ut.is_quit = '0'
            AND t.status = '0'
            AND t.del_flag = '0'
        ORDER BY 
            ut.is_default DESC, 
            ut.join_time DESC
    </select>

    <!-- 获取用户的所有租户及云信账号信息 -->
    <select id="selectUserTenantAndIMInfoList" resultMap="TenantIMInfoResultMap">
        SELECT 
            t.tenant_id,
            t.tenant_name,
            t.tenant_code,
            t.logo,
            t.industry,
            t.scale,
            t.address,
            t.contact_user,
            t.contact_phone,
            t.domain,
            t.intro,
            t.status,
            t.expire_time,
            t.account_count,
            ut.is_admin,
            ut.nick_name,
            ut.avatar,            
            ut.position,
            ut.join_time,
            ut.is_quit,
            i.id AS im_id,
            i.user_id AS im_user_id,
            i.global_user_id AS im_global_user_id,
            i.yx_account_id,
            i.yx_token,
            i.status AS im_status,
            i.create_time AS im_create_time,
            i.update_time AS im_update_time
        FROM 
            qwx_user_tenant ut
        INNER JOIN 
            qwx_tenant t ON ut.tenant_id = t.tenant_id
        LEFT JOIN 
            im_yxaccount_mapping i ON ut.global_user_id = i.global_user_id AND ut.tenant_id = i.tenant_id
        WHERE 
            ut.global_user_id = #{globalUserId}
            AND ut.status = '0'
            AND ut.is_quit = '0'
            AND t.status = '0'
            AND t.del_flag = '0'
        ORDER BY 
            ut.join_time DESC
    </select>
    
    <!-- 获取用户的指定租户及云信账号信息 -->
    <select id="selectSingleUserTenantAndIMInfo" resultMap="TenantIMInfoResultMap">
        SELECT 
            t.tenant_id,
            t.tenant_name,
            t.tenant_code,
            t.logo,
            t.industry,
            t.scale,
            t.address,
            t.contact_user,
            t.contact_phone,
            t.domain,
            t.intro,
            t.status,
            t.expire_time,
            t.account_count,
            ut.is_admin,
            ut.nick_name,
            ut.avatar,            
            ut.position,
            ut.join_time,
            ut.is_quit,
            i.id AS im_id,
            i.user_id AS im_user_id,
            i.global_user_id AS im_global_user_id,
            i.yx_account_id,
            i.yx_token,
            i.status AS im_status,
            i.create_time AS im_create_time,
            i.update_time AS im_update_time
        FROM 
            qwx_user_tenant ut
        INNER JOIN 
            qwx_tenant t ON ut.tenant_id = t.tenant_id
        LEFT JOIN 
            im_yxaccount_mapping i ON ut.global_user_id = i.global_user_id AND ut.tenant_id = i.tenant_id
        WHERE 
            ut.global_user_id = #{globalUserId}
            AND ut.tenant_id = #{tenantId}
            AND ut.status = '0'
            AND ut.is_quit = '0'
            AND t.status = '0'
            AND t.del_flag = '0'
        LIMIT 1
    </select>

</mapper>
