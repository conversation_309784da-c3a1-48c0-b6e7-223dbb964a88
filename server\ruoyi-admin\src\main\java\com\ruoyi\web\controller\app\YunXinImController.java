package com.ruoyi.web.controller.app;

import com.ruoyi.web.service.YunXinImService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 网易云信回调&抄送
 * <AUTHOR>
 * @date 2025/5/22 下午5:35
 */
@Api(tags = "网易云信回调&抄送")
@RestController
@RequestMapping("/app/yunxin")
public class YunXinImController {
    @Resource
    private YunXinImService yunXinImService;

    /**
     * 网易云信回调接收接口
     * 根据文档：https://doc.yunxin.163.com/messaging/server-apis/jI3ODc2ODE?platform=server
     * 回调请求通常是POST请求，请求体中包含回调数据
     *
     * @param callbackData 回调数据，具体类型需要根据网易云信回调文档定义
     * @return 响应结果
     */
    @ApiOperation("网易云信回调接收接口")
    @PostMapping("/callback")
    public String receiveYunXinCallback(@RequestBody String callbackData) {
        return yunXinImService.callBack(callbackData);
    }

    /**
     * 网易云信抄送接收接口
     * 根据文档：https://doc.yunxin.163.com/messaging/server-apis/jI3ODc2ODE?platform=server
     * 抄送请求通常是POST请求，请求体中包含抄送数据
     *
     * @param copyData 抄送数据，具体类型需要根据网易云信抄送文档定义
     * @return 响应结果
     */
    @ApiOperation("网易云信抄送接收接口")
    @PostMapping("/copy")
    public String receiveYunXinCopy(@RequestBody String copyData) {
        return yunXinImService.copy(copyData);
    }
}
