package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.app.domain.QwxTenantConfig;
import com.ruoyi.app.service.QwxTenantConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 租户配置Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/tenant/config")
public class QwxTenantConfigController extends BaseController {
    
    @Resource
    private QwxTenantConfigService qwxTenantConfigService;

    /**
     * 查询租户配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(QwxTenantConfig tenantConfig) {
        startPage();
        List<QwxTenantConfig> list = qwxTenantConfigService.selectTenantConfigList(tenantConfig);
        return getDataTable(list);
    }

    /**
     * 导出租户配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:config:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, QwxTenantConfig tenantConfig) {
        List<QwxTenantConfig> list = qwxTenantConfigService.selectTenantConfigList(tenantConfig);
        ExcelUtil<QwxTenantConfig> util = new ExcelUtil<>(QwxTenantConfig.class);
        util.exportExcel(response, list, "租户配置数据");
    }

    /**
     * 获取租户配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:config:query')")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable("configId") Long configId) {
        return success(qwxTenantConfigService.getById(configId));
    }
    
    /**
     * 根据租户ID获取配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:config:query')")
    @GetMapping(value = "/tenant/{tenantId}")
    public AjaxResult getConfigByTenantId(@PathVariable("tenantId") Long tenantId) {
        return success(qwxTenantConfigService.getConfigByTenantId(tenantId));
    }
    
    /**
     * 获取配置值
     */
    @GetMapping(value = "/value/{tenantId}/{configKey}")
    public AjaxResult getConfigValue(@PathVariable("tenantId") Long tenantId, @PathVariable("configKey") String configKey) {
        String value = qwxTenantConfigService.getConfigValue(tenantId, configKey);
        return StringUtils.isNotEmpty(value) ? success(value) : error("未找到配置项");
    }

    /**
     * 新增租户配置
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:config:add')")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody QwxTenantConfig tenantConfig) {
        return toAjax(qwxTenantConfigService.insertTenantConfig(tenantConfig));
    }

    /**
     * 修改租户配置
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:config:edit')")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody QwxTenantConfig tenantConfig) {
        return toAjax(qwxTenantConfigService.updateTenantConfig(tenantConfig));
    }
    
    /**
     * 批量更新租户配置
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:config:edit')")
    @PutMapping("/batch/{tenantId}")
    public AjaxResult updateBatch(@PathVariable("tenantId") Long tenantId, @Validated @RequestBody List<QwxTenantConfig> configs) {
        return toAjax(qwxTenantConfigService.updateBatch(tenantId, configs));
    }

    /**
     * 删除租户配置
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:config:remove')")
    @DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds) {
        return toAjax(qwxTenantConfigService.deleteTenantConfigByIds(configIds));
    }
    
    /**
     * 重置为默认配置
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:config:edit')")
    @PutMapping("/reset/{tenantId}")
    public AjaxResult resetToDefault(@PathVariable("tenantId") Long tenantId) {
        return toAjax(qwxTenantConfigService.resetToDefault(tenantId));
    }
} 