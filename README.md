# 微企信管理系统APP项目需求文档

## 项目简介

微企信管理系统是一款基于Vue.js和Element UI的单页面管理系统，旨在帮助企业实现高效的客户管理、数据分析等功能。

类似企业微信的多租户APP项目需求文档。

**项目需求文档：多租户企业协作与通讯平台APP**

**1. 项目概述**

*   **项目名称：** [微企信]
*   **项目愿景：** 打造一个安全、高效、灵活的多租户企业通讯平台，允许用户使用单一手机账号管理和切换多个企业/组织身份，赋能企业内部沟通、协作与管理。
*   **灵感来源：** 企业微信
*   **核心特性：** 多租户支持、单一手机号或邮箱进行多组织登录、即时通讯（单聊、群聊、语音、视频、文件）、组织架构管理。


![业务流程图](doc/业务流程图.png)


**2. 目标用户**

*   **企业/组织管理员：**
    *   创建和管理企业/组织空间。
    *   管理组织架构（部门、成员、角色）。
    *   配置企业应用和权限。
    *   监控平台使用情况。
*   **企业/组织普通成员：**
    *   进行内部即时通讯（单聊、群聊、语音、视频、文件）。
    *   查看组织通讯录。
    *   在多个已加入的企业/组织间轻松切换。

**3. 核心功能需求 (Functional Requirements)**

**3.1. 用户账户与认证体系**

*   **3.1.1. 全局用户注册：**
    *   用户使用手机号 + 短信验证码注册全局账户。
    *   用户使用邮箱 + 密码注册全局账户。
    *   设置登录密码。
    *   完善基础个人信息（昵称、头像等）。
*   **3.1.2. 全局用户登录：**
    *   支持手机号 + 密码登录。
    *   支持手机号 + 短信验证码登录。
    *   支持邮箱 + 密码登录。
    *   提供“忘记密码”功能，通过手机验证码重置。
*   **3.1.3. 多组织切换：**
    *   登录后，如果用户属于多个组织，系统应展示组织列表供用户选择进入。
    *   在APP内提供便捷的组织切换入口。
*   **3.1.4. 个人资料管理：**
    *   用户可修改全局账户的昵称、头像、密码等。
    *   用户可在特定组织内设置该组织下的昵称、职位等（与全局信息可不同）。

**3.2. 组织/租户管理**

*   **3.2.1. 创建组织：**
    *   认证用户可以创建新的企业/组织。
    *   创建时需填写组织名称、行业、规模等基础信息。
    *   创建者自动成为该组织的超级管理员。
*   **3.2.2. 组织信息配置：**
    *   管理员可修改组织名称、Logo、简介等。
    *   可配置组织特有的欢迎语、登录页背景等（可选）。
*   **3.2.3. 组织成员管理：**
    *   **邀请成员：** 
        * 管理员可通过手机号、邮箱、邀请链接、二维码等方式邀请成员加入。
        * 支持生成邀请码（可设置有效期和使用人数上限）。
    *   **成员审批：** 可设置新成员加入是否需要管理员审批。
    *   **成员列表：** 展示组织内所有成员，支持搜索、按部门筛选。
    *   **编辑成员信息：** 管理员可修改成员在组织内的信息（如所属部门、职位、权限等）。
    *   **禁用/删除成员：** 管理员可禁用或移除组织成员。
*   **3.2.4. 组织架构管理：**
    *   支持创建、编辑、删除多层级部门。
    *   支持将成员分配到不同部门。
    *   支持设置部门负责人。
*   **3.2.5. 角色与权限管理 (RBAC - 基础版)：**
    *   预设几种常用角色（如：超级管理员、部门管理员、普通成员）。
    *   超级管理员可管理组织内所有事务。
    *   后续可扩展自定义角色和细粒度权限配置。

**3.3. 独立部署需求**
*   **3.3.1. 私有化部署支持：**
    *   提供完整的私有化部署方案，支持部署在企业内部服务器或私有云环境。
    *   提供Docker镜像和Kubernetes部署配置文件。
    *   支持Linux/Windows服务器部署。
    *   独立部署版本需通过SaaS平台服务获取授权Token进行登录验证。
*   **3.3.2. 数据库支持：**
    *   支持MySQL/PostgreSQL作为主数据库。
    *   支持Redis作为缓存和消息队列。
    *   提供数据库初始化脚本和迁移工具。
*   **3.3.3. 存储配置：**
    *   支持本地文件系统存储。
    *   支持对接阿里云OSS、腾讯云COS等对象存储。
    *   提供存储配置管理界面。
*   **3.3.4. 网络要求：**
    *   明确服务器最低网络带宽要求。
    *   提供内网穿透方案建议。
    *   需保持与SaaS平台服务的网络连通以获取授权Token。
*   **3.3.5. 系统监控：**
    *   集成Prometheus监控指标。
    *   提供Grafana监控面板模板。
    *   支持日志集中收集和分析。
*   **3.3.6. 备份与恢复：**
    *   提供数据库备份工具和脚本。
    *   支持定期自动备份。
    *   提供灾难恢复方案。
*   **3.3.7. 升级维护：**
    *   提供平滑升级方案。
    *   支持版本回滚。
    *   提供维护模式开关。
*   **3.3.8. 授权认证：**
    *   独立部署版本需通过SaaS平台进行统一身份认证。
    *   提供Token获取接口，用于验证部署实例的合法性。
    *   支持Token自动续期机制，确保服务连续性。
    *   提供离线授权模式，在网络中断时有限时间内仍可正常使用。


**3.4. 即时通讯 (IM)**

*   **3.4.1. 单聊：**
    *   支持与组织内成员发起一对一聊天。
    *   支持发送文本、表情、图片、短视频、文件、语音消息。
    *   显示消息已读/未读状态。
    *   聊天记录云端同步与本地缓存。
*   **3.4.2. 群聊：**
    *   支持创建群聊，邀请组织内成员加入。
    *   群主/管理员可管理群成员（邀请、移除）、修改群公告、群名称。
    *   支持@提及功能。
    *   群聊消息同样支持多种消息类型和已读状态（或已读成员列表）。
*   **3.4.3. 通讯录：**
    *   清晰展示组织架构和成员列表。
    *   支持按姓名、手机号、拼音、部门搜索成员。
    *   查看成员名片（头像、姓名、部门、职位、手机号 - 手机号可见性可配置）。
*   **3.4.4. 消息通知：**
    *   新消息推送通知（App内、系统通知栏）。
    *   可设置免打扰模式（全局、特定会话）。

**3.5. 基础应用 (MVP阶段可选)**

![UI设计图](doc/ui.png)


*   **3.5.1. MVP核心功能：**
    *   **用户模块：**
        *   手机号注册/登录
        *   查看个人基本信息（"我的"页面）
        *   退出登录
    *   **聊天模块：**
        *   聊天会话列表（展示单聊、群聊，未读数，最后消息）
        *   单聊功能（发送/接收文本、图片）
        *   群聊功能（创建群、加入群、发送/接收文本、图片）
        *   新消息通知
    *   **联系人模块：**
        *   好友列表展示
        *   查看好友资料
        *   添加好友（通过搜索或处理请求）
        *   设置好友备注
        *   删除好友
    *   **群组模块：**
        *   查看群信息（群名、群成员）
        *   群消息免打扰
    *   **基础交互：**
        *   底部Tab导航（聊天、好友、我的）
        *   页面返回
        *   搜索好友

*   **3.5.2. 设计时要预留后续需要支持的拓展功能：**
    *   钱包功能
    *   标签管理
    *   高级群管理功能（群公告、群主转让、群二维码等）
    *   个人资料详细编辑
    *   用户协议/隐私协议具体页面
    *   语音/视频/文件消息完整支持
    *   聊天记录云端同步与多端漫游
    *   通知特殊处理逻辑

**3.6. 管理后台 (Web端)**

*   提供给组织管理员使用的Web管理后台，用于进行复杂的组织管理、成员管理、权限配置、应用管理、数据统计等。

**3.7. 网易云信SDK集成**

*   **3.7.1. 核心功能实现：**
    *   所有实时通信功能基于网易云信SDK开发
    *   单聊/群聊消息收发通过SDK原生接口实现
    *   消息通知推送使用SDK提供的推送能力
    *   消息历史记录存储和同步依赖SDK服务
*   **3.7.2. 系统集成方式：**
    *   App端直接集成云信SDK进行通信
    *   服务端仅维护用户与云信账号的映射关系
    *   管理后台通过云信服务端API拉取通信数据
*   **3.7.3. 功能特点：**
    *   支持消息多端同步
    *   提供消息已读回执功能
    *   内置完善的网络重连机制
    *   支持离线消息推送
*   **3.7.4. 管理后台对接：**
    *   通过REST API获取聊天记录
    *   支持按时间/会话/用户查询消息
    *   可获取用户在线状态
    *   支持禁言等基础管理功能
*   **3.7.5. 优势说明：**
    *   减少自研IM系统的开发成本
    *   直接使用成熟的通信架构
    *   享受网易云信的消息安全保障
    *   可快速上线核心通信功能


**4. 非功能性需求 (Non-Functional Requirements)**

*   **4.1. 安全性：**
    *   **数据隔离：** 严格保证不同租户（组织）之间的数据隔离。
    *   **传输安全：** 通讯内容、API调用使用HTTPS/TLS加密。
    *   **存储安全：** 敏感数据（如密码）加密存储。
    *   **权限控制：** 严格的权限校验，防止越权操作。
    *   **防攻击：** 具备基础的防DDoS、防SQL注入等安全机制。
*   **4.2. 性能：**
    *   **响应速度：** 核心操作（如登录、消息发送/接收、组织切换）响应时间应在可接受范围内（如：95%请求 < 200ms）。
    *   **并发处理：** 支持一定数量的并发用户在线和消息收发。
*   **4.3. 可用性与可靠性：**
    *   系统应具备高可用性，减少服务中断时间。
    *   消息传递可靠，不丢失。
    *   数据备份与恢复机制。
*   **4.4. 可扩展性：**
    *   系统架构设计应考虑未来的功能扩展和用户量增长。
    *   支持水平扩展后端服务。
*   **4.5. 易用性：**
    *   界面简洁直观，操作便捷。
    *   符合主流IM和企业协作软件用户习惯。

**5. 项目结构/系统架构设想 (高层次)**

*   **5.1. 客户端 (APP)：**
    *   iOS App
    *   Android App
    *   技术选型：Native (Swift/Kotlin), React Native, Flutter 等。
*   **5.2. 服务端 (Backend)：**
    *   **API网关：** 请求路由、认证、限流、日志等。
    *   **用户中心服务 (UC)：** 负责全局用户注册、登录、信息管理。
    *   **组织/租户服务：** 负责组织创建、管理、成员、部门、权限等。核心的多租户逻辑实现。
    *   **IM服务：** 负责单聊、群聊、消息持久化、消息推送。可能使用WebSocket或类似技术。
    *   **应用服务：** 负责公告、审批等协作应用逻辑。
    *   **通知服务：** 负责App推送、短信通知等。
    *   **管理后台服务 (Admin Web API)：** 为Web管理后台提供API。
*   **5.3. 数据库：**
    *   **关系型数据库 (如 PostgreSQL, MySQL)：** 存储用户信息、组织信息、部门、权限等结构化数据。关键表需要有 `tenant_id` (组织ID) 字段以实现数据隔离。
    *   **NoSQL数据库 (如 MongoDB, Cassandra - 可选，用于IM)：** 存储聊天记录等非结构化或半结构化数据，便于横向扩展。
    *   **缓存数据库 (如 Redis)：** 存储会话信息、热点数据、消息序列号等，提升性能。
*   **5.4. 第三方服务：**
    *   **短信服务：** 用于手机验证码。
    *   **消息推送服务：** (如 APNS, FCM, 或第三方推送平台如极光推送、个推)。
    *   **对象存储服务 (如 S3, OSS)：** 存储用户头像、聊天发送的图片、文件等。

**6. 数据模型核心实体 (Conceptual Data Model)**


**7. 关键技术挑战**

*   **多租户数据隔离与安全：** 确保租户数据的严格隔离是首要任务。
*   **实时消息系统：** 高并发、低延迟、高可靠的消息传递。
*   **账户体系与多组织身份管理：** 用户如何在多个组织中平滑切换并保持正确的上下文。
*   **权限系统设计：** 既要灵活又要易于管理。
*   **系统扩展性：** 随着用户和组织数量的增长，系统需要能够平滑扩展。

**8. 后续迭代方向 (Future Considerations)**

*   更丰富的协作应用（日程、任务、文档协作等）。
*   开放平台，支持第三方应用接入。
*   音视频通话/会议。
*   更精细化的权限管理。
*   数据分析与报表。
*   桌面客户端。

**免责声明：**
本文档仅为初步需求构思，供项目启动参考。详细设计阶段需要进行更深入的需求分析、技术选型论证和原型设计。
