package com.ruoyi.web.interceptor;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.app.domain.QwxGlobalUser;
import com.ruoyi.app.model.vo.AppUserTenantIMVo;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.web.service.AppUserTokenService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * APP Token拦截器
 * <AUTHOR>
 * @date 2025/5/11 下午11:50
 */
@Component
public class AppTokenInterceptor implements HandlerInterceptor {
    private static final Logger log = LoggerFactory.getLogger(AppTokenInterceptor.class);

    @Resource
    private AppUserTokenService appUserTokenService;

    /**
     * 请求前处理，验证token并设置用户和租户信息到上下文
     * 
     * @param request 请求对象
     * @param response 响应对象
     * @param handler 处理器
     * @return 是否继续执行
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取token
        String token = appUserTokenService.getToken(request);
        
        // 如果token为空，返回未授权错误
        if (StringUtils.isEmpty(token)) {
            unauthorizedResponse(response, "令牌不能为空");
            return false;
        }

        boolean plToken = appUserTokenService.checkToken(token);
        // 验证租户令牌
        boolean isValid = appUserTokenService.checkTenantToken(token);
        if (!isValid && !plToken) {
            unauthorizedResponse(response, "令牌已过期或无效");
            return false;
        }
        
        // 获取用户信息和租户信息
        QwxGlobalUser globalUser = appUserTokenService.getAppLoginUser(request);
        AppUserTenantIMVo.TenantIMInfo tenantInfo = appUserTokenService.getTenantInfo(request);
        
        if (globalUser == null && tenantInfo == null) {
            log.error("租户令牌解析失败，无法获取用户或租户信息");
            unauthorizedResponse(response, "获取用户或租户信息失败，请重新登录");
            return false;
        }
        
        // 检查用户状态
        if (!"0".equals(globalUser.getStatus())) {
            unauthorizedResponse(response, "用户已被禁用");
            return false;
        }
        
        // 检查租户状态
        if (tenantInfo != null && !"0".equals(tenantInfo.getTenantStatus())) {
            unauthorizedResponse(response, "组织已被禁用");
            return false;
        }
        
        // 将用户信息和租户信息放入request中
        // 注意: 保持参数名称与AppContext中的方法一致，确保能够正确获取
        request.setAttribute("appUser", globalUser);
        request.setAttribute("tenantInfo", tenantInfo);
        request.setAttribute("token", token);
        
        if (log.isDebugEnabled()) {
            log.debug("设置租户上下文成功: 用户ID={}", globalUser.getGlobalUserId());
            if (tenantInfo != null) {
                log.debug("设置租户上下文成功: 租户ID={}", tenantInfo.getTenantId());
            }
        }
        
        return true;
    }
    
    /**
     * 返回未授权错误信息
     * 
     * @param response 响应对象
     * @param msg 错误信息
     */
    private void unauthorizedResponse(HttpServletResponse response, String msg) throws IOException {
        log.warn("授权验证失败: {}", msg);
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(JSON.toJSONString(AjaxResult.error(HttpStatus.UNAUTHORIZED.value(), msg)));
    }
} 