# WebSocket接口文档

## 1. 连接信息

### 1.1 WebSocket接口地址

- 请求头需要携带当前组织的token

```
Authorization:{token}

ws://8.138.209.108:8080/app/ws
```


## 2. 心跳包机制

为保持WebSocket连接的稳定性，客户端需要定期向服务器发送心跳包。

### 2.1 心跳包发送

- 客户端需要**每15-30秒**主动向服务端发送一次心跳包
- 如果客户端超过**45秒**未发送心跳包，服务端会认为客户端已断开连接
- 服务端会自动将超时用户状态更新为离线，并断开WebSocket连接

### 2.2 心跳包格式

客户端发送心跳包：

```json
{
  "type": "heartbeat_ping",
  "time": 1684123456789
}
```

服务端回复确认：

```json
{
  "type": "heartbeat_ack",
  "time": 1684123456890
}
```

| 字段 | 类型 | 说明 |
| ---- | ---- | ---- |
| type | String | 消息类型，固定为"heartbeat_ping"或"heartbeat_ack" |
| time | Long | 时间戳（毫秒） |

## 3. 消息类型

### 3.1 连接成功

当WebSocket连接成功建立时，服务端会发送连接成功消息：

```json
{
  "type": "connected",
  "tenantId": "10001", 
  "userId": "user123456",
  "onlineCount": 125
}
```

| 字段 | 类型 | 说明 |
| ---- | ---- | ---- |
| type | String | 消息类型，固定为"connected" |
| tenantId | String | 租户ID |
| userId | String | 用户ID |
| onlineCount | Integer | 当前在线人数 |


## 4. 在线状态

用户在线状态说明：

| 状态码 | 状态描述 |
| ------ | -------- |
| 0 | 离线 |
| 1 | 在线 |
| 2 | 隐身 |

在线状态变更条件：
- 连接建立时，用户状态自动设为"在线(1)"
- 连接断开或心跳超时时，用户状态自动设为"离线(0)"
- 如果用户通过接口手动改为隐身状态，则心跳连接的状态不做任何修改

## 5. 错误处理

### 5.1 连接错误

当WebSocket连接发生错误时，服务端会主动断开连接，客户端需要实现重连机制。

### 5.2 心跳超时

当客户端超过45秒未发送心跳包时，服务端会：
1. 将用户状态设置为"离线"
2. 断开WebSocket连接

### 5.3 重连建议

客户端应实现指数退避重连机制：
- 首次断开后立即尝试重连
- 重连失败后，按指数增长的时间间隔重试（如：1秒、2秒、4秒、8秒...最大不超过60秒）
- 网络恢复后尝试重新建立连接

## 6. 开发建议

1. 使用专门的心跳定时器，确保心跳包按时发送
2. 实现自动重连机制，提高连接稳定性
3. 在移动端注意处理应用进入后台、恢复前台的场景
4. 处理网络状态变化事件，及时调整连接策略
5. 记录并上报连接异常，便于问题排查

