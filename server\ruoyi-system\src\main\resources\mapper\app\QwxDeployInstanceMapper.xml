<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.QwxDeployInstanceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.QwxDeployInstance">
        <id column="instance_id" property="instanceId" />
        <result column="tenant_id" property="tenantId" />
        <result column="instance_name" property="instanceName" />
        <result column="instance_code" property="instanceCode" />
        <result column="public_ip" property="publicIp" />
        <result column="private_ip" property="privateIp" />
        <result column="domain" property="domain" />
        <result column="license_key" property="licenseKey" />
        <result column="deploy_env" property="deployEnv" />
        <result column="max_users" property="maxUsers" />
        <result column="version" property="version" />
        <result column="heartbeat_time" property="heartbeatTime" />
        <result column="heartbeat_interval" property="heartbeatInterval" />
        <result column="status" property="status" />
        <result column="expire_time" property="expireTime" />
        <result column="offline_auth_days" property="offlineAuthDays" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        instance_id, tenant_id, instance_name, instance_code, public_ip, private_ip, domain, license_key, deploy_env, max_users, version, heartbeat_time, heartbeat_interval, status, expire_time, offline_auth_days, create_time, update_time, remark
    </sql>

</mapper>
