package com.ruoyi.web.controller.app;

import com.ruoyi.app.model.dto.JoinTenantDto;
import com.ruoyi.app.model.dto.LoginDto;
import com.ruoyi.app.model.dto.RefreshTokenDto;
import com.ruoyi.app.model.vo.AppLoginResultVo;
import com.ruoyi.app.model.vo.AppUserTenantIMVo;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.web.service.LoginService;
import com.ruoyi.web.service.TenantConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * APP认证控制器
 *
 * <AUTHOR>
 * @date 2025/5/11 下午11:30
 */
@Api(tags = "APP用户认证")
@RestController
@RequestMapping("/app/auth")
public class AppAuthController {

    @Resource
    private LoginService loginService;
    @Resource
    private TenantConfigService tenantConfigService;

    /**
     * APP用户登录接口
     *
     * @param loginDto 登录参数
     * @return 登录结果
     */
    @PostMapping("/login")
    @ApiOperation(value = "登录或注册", notes = "通过手机号/邮箱+验证码方式登录或注册")
    public R<AppLoginResultVo> login(@RequestBody @Validated LoginDto loginDto) {
        return loginService.login(loginDto);
    }


    /**
     * 发送验证码
     *
     * @param loginDto 登录参数
     * @return 操作结果
     */
    @PostMapping("/sendCode")
    @ApiOperation("发送验证码")
    public AjaxResult sendCode(@RequestBody @Validated LoginDto loginDto) {
        return loginService.sendCode(loginDto);
    }


    /**
     * 用户注销
     *
     * @return 操作结果
     */
    @PostMapping("/logout")
    @ApiOperation("用户注销")
    public AjaxResult logout() {
        return loginService.logout();
    }

    /**
     * 通过邀请码加入租户
     *
     * @param joinTenantDto 加入租户DTO
     * @return 操作结果
     */
    @PostMapping("/joinTenant")
    @ApiOperation(value = "加入组织", notes = "通过邀请码加入租户组织")
    public R<AppUserTenantIMVo.TenantIMInfo> joinTenant(@RequestBody @Validated JoinTenantDto joinTenantDto) {
        return tenantConfigService.joinTenantByInviteCode(joinTenantDto);
    }

    /**
     * 刷新token
     *
     * @param refreshTokenDto 刷新token请求参数
     * @return 刷新后的token信息
     */
    @PostMapping("/refreshToken")
    @ApiOperation(value = "刷新token", notes = "使用刷新令牌获取新的访问token，适用于token已过期的情况")
    public R<AppLoginResultVo> refreshToken(@RequestBody @Validated RefreshTokenDto refreshTokenDto) {
        return loginService.refreshToken(refreshTokenDto.getRefreshToken());
    }
} 