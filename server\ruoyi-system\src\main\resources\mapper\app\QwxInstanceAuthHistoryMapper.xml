<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.QwxInstanceAuthHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.QwxInstanceAuthHistory">
        <id column="history_id" property="historyId" />
        <result column="instance_id" property="instanceId" />
        <result column="token_id" property="tokenId" />
        <result column="auth_type" property="authType" />
        <result column="auth_result" property="authResult" />
        <result column="auth_time" property="authTime" />
        <result column="expire_time" property="expireTime" />
        <result column="ip_address" property="ipAddress" />
        <result column="error_msg" property="errorMsg" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        history_id, instance_id, token_id, auth_type, auth_result, auth_time, expire_time, ip_address, error_msg, create_time
    </sql>

</mapper>
