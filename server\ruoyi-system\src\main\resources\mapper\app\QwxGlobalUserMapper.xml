<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.QwxGlobalUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.QwxGlobalUser">
        <id column="global_user_id" property="globalUserId" />
        <result column="user_name" property="userName" />
        <result column="password" property="password" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="sex" property="sex" />
        <result column="is_perfect_info" property="isPerfectInfo" />
        <result column="register_type" property="registerType" />
        <result column="register_time" property="registerTime" />
        <result column="register_ip" property="registerIp" />
        <result column="last_login_time" property="lastLoginTime" />
        <result column="last_login_ip" property="lastLoginIp" />
        <result column="status" property="status" />
        <result column="del_flag" property="delFlag" />
        <result column="wx_openid" property="wxOpenid" />
        <result column="wx_unionid" property="wxUnionid" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        global_user_id, user_name, password, phone, email, sex, is_perfect_info, register_type, register_time, register_ip, last_login_time, last_login_ip, status, del_flag, wx_openid, wx_unionid, create_time, update_time, remark
    </sql>

</mapper>
