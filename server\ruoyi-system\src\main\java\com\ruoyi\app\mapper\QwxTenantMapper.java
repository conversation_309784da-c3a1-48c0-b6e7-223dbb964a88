package com.ruoyi.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.app.domain.QwxTenant;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 租户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
public interface QwxTenantMapper extends BaseMapper<QwxTenant> {
 /**
     * 查询租户列表
     *
     * @param qwxTenant 租户
     * @return 租户集合
     */
    List<QwxTenant> selectQwxTenantList(QwxTenant qwxTenant);

    /**
     * 查询当前用户的默认组织
     * 
     * @param globalUserId 用户ID
     * @return 租户信息
     */
    QwxTenant selectDefaultTenantByUserId(@Param("globalUserId") String globalUserId);
}
