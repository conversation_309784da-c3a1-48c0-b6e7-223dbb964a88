<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.2 Chrome/134.0.6998.205 Electron/35.3.0 Safari/537.36" version="27.0.2">
  <diagram id="MainDiagram" name="微企信业务流程">
    <mxGraphModel dx="1628" dy="850" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="2336" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <object label="用户注册与登录流程" id="flow1-title">
          <mxCell style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;" parent="1" vertex="1">
            <mxGeometry x="40" y="20" width="280" height="30" as="geometry" />
          </mxCell>
        </object>
        <mxCell id="user-start" value="开始" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="120" y="70" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="open-app" value="用户打开APP&lt;br&gt;(UI 1: 微企信启动页)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#343a40;" parent="1" vertex="1">
          <mxGeometry x="100" y="170" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="action-choice" value="选择操作？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="110" y="280" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="register-phone" value="选择手机号注册" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="320" y="285" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="register-email" value="选择邮箱注册" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="320" y="385" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="enter-phone-code" value="输入手机号&lt;br&gt;获取/输入验证码" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="480" y="285" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="enter-email-pass" value="输入邮箱&lt;br&gt;设置密码" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="480" y="385" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="set-global-info" value="完善全局个人信息&lt;br&gt;(昵称、头像等)" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="650" y="330" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="register-success" value="全局账户创建成功" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="830" y="335" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="login" value="选择登录" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="120" y="400" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="login-method" value="选择登录方式？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="110" y="490" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="login-phone-pass" value="手机号+密码登录" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="320" y="480" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="login-phone-code" value="手机号+验证码登录" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="320" y="555" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="login-email-pass" value="邮箱+密码登录" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="320" y="630" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="enter-credentials" value="输入凭证" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="480" y="545" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="verify-credentials" value="系统验证凭证" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="600" y="545" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="login-fail" value="验证失败？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="760" y="535" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="forgot-password" value="忘记密码流程" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="760" y="680" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="login-success" value="登录成功" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="990" y="545" width="100" height="70" as="geometry" />
        </mxCell>
        <mxCell id="check-multi-org" value="用户是否属于多个组织?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="1110" y="325" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="show-org-list" value="展示组织列表&lt;br&gt;供用户选择" style="shape=display;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1310" y="330" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="select-org" value="用户选择一个组织进入" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1310" y="440" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="main-app-interface" value="进入APP主界面&lt;br&gt;(UI 5: 聊天列表页)" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="1315" y="540" width="150" height="90" as="geometry" />
        </mxCell>
        <object label="核心通讯与联系人管理流程 (MVP)" id="flow2-title">
          <mxCell style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;" parent="1" vertex="1">
            <mxGeometry x="40" y="750" width="380" height="30" as="geometry" />
          </mxCell>
        </object>
        <mxCell id="app-main-start" value="APP主界面&lt;br&gt;(底部Tab: 聊天, 好友, 我的)" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="100" y="800" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="select-tab" value="用户选择Tab？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="300" y="805" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="chat-tab" value="聊天Tab&lt;br&gt;(UI 5)" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#f8f9fa;strokeColor=#343a40;" parent="1" vertex="1">
          <mxGeometry x="480" y="780" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="friends-tab" value="好友Tab&lt;br&gt;(UI 3, UI 4)" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#f8f9fa;strokeColor=#343a40;" parent="1" vertex="1">
          <mxGeometry x="480" y="860" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="my-tab" value="我的Tab&lt;br&gt;(UI 10)" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#f8f9fa;strokeColor=#343a40;" parent="1" vertex="1">
          <mxGeometry x="480" y="940" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="chat-list-action" value="查看聊天列表&lt;br&gt;选择会话/发起新聊天" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#f8f9fa;strokeColor=#343a40;" parent="1" vertex="1">
          <mxGeometry x="640" y="770" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="chat-screen" value="进入聊天界面&lt;br&gt;(UI 9: 私聊)&lt;br&gt;发送/接收消息(文本/图片)" style="shape=display;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#343a40;" parent="1" vertex="1">
          <mxGeometry x="830" y="765" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="new-message-notif" value="新消息通知" style="shape=message;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1175" y="720" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="friends-list-action" value="查看好友列表 (UI 4)&lt;br&gt;或 好友请求 (UI 3)" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#f8f9fa;strokeColor=#343a40;" parent="1" vertex="1">
          <mxGeometry x="640" y="850" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="friend-action-choice" value="操作类型？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="830" y="850" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="add-friend" value="添加好友&lt;br&gt;(搜索/处理请求)" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#f8f9fa;strokeColor=#343a40;" parent="1" vertex="1">
          <mxGeometry x="1010" y="965" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="view-friend-profile" value="查看好友资料&lt;br&gt;(UI 2: 安道尔-接待-小云)" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#f8f9fa;strokeColor=#343a40;" parent="1" vertex="1">
          <mxGeometry x="1185" y="810" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="set-remark-delete" value="设置备注/删除好友" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#f8f9fa;strokeColor=#343a40;" parent="1" vertex="1">
          <mxGeometry x="1190" y="965" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="my-profile-view" value="查看个人信息&lt;br&gt;(UI 10: 钱包/账号/设置等)" style="shape=display;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#343a40;" parent="1" vertex="1">
          <mxGeometry x="640" y="930" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="logout-action" value="退出登录" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="830" y="940" width="120" height="60" as="geometry" />
        </mxCell>
        <object label="群组管理流程 (MVP)" id="flow3-title">
          <mxCell style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;" parent="1" vertex="1">
            <mxGeometry x="40" y="1050" width="280" height="30" as="geometry" />
          </mxCell>
        </object>
        <mxCell id="group-start" value="从好友Tab或聊天Tab" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="80" y="1100" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="group-action-choice" value="群组操作？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="280" y="1100" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="create-group" value="创建群聊" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="460" y="1070" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="invite-to-group" value="邀请成员加入群聊" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="930" y="1070" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="join-group" value="加入群聊&lt;br&gt;(通过邀请/搜索等)" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="460" y="1150" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="view-group-info" value="查看群信息&lt;br&gt;(UI: 群聊信息页)&lt;br&gt;群成员/群名称等" style="shape=display;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#343a40;" parent="1" vertex="1">
          <mxGeometry x="630" y="1140" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="group-settings" value="群设置&lt;br&gt;(群消息免打扰等)" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#f8f9fa;strokeColor=#343a40;" parent="1" vertex="1">
          <mxGeometry x="820" y="1150" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="group-chat-screen" value="进入群聊界面&lt;br&gt;发送/接收消息(文本/图片)" style="shape=display;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#343a40;" parent="1" vertex="1">
          <mxGeometry x="450" y="1240" width="160" height="90" as="geometry" />
        </mxCell>
        <object label="组织管理与切换流程 (多租户核心)" id="flow4-title">
          <mxCell style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;" parent="1" vertex="1">
            <mxGeometry x="40" y="1380" width="380" height="30" as="geometry" />
          </mxCell>
        </object>
        <mxCell id="org-start" value="已登录用户" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="100" y="1430" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="org-action-choice" value="组织相关操作?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="260" y="1420" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="create-new-org" value="创建新组织&lt;div&gt;（只有平台可以创建）&lt;/div&gt;" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="440" y="1385" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fill-org-info" value="填写组织信息&lt;br&gt;(名称,行业,规模等)" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="600" y="1380" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="org-created" value="组织创建成功&lt;br&gt;创建者为超管" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="780" y="1380" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="switch-org" value="切换组织" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="440" y="1510" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="display-user-orgs" value="显示用户所属组织列表" style="shape=display;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="600" y="1505" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="user-selects-org-switch" value="用户选择目标组织" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="780" y="1510" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="context-switched" value="切换到所选组织上下文&lt;br&gt;(通讯录,聊天等变更)" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="950" y="1500" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="join-existing-org" value="加入已有组织" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="440" y="1590" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="join-method" value="通过邀请链接/码/搜索等" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="600" y="1585" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="approval-needed" value="是否需要审批?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="770" y="1580" width="130" height="80" as="geometry" />
        </mxCell>
        <mxCell id="admin-approves" value="管理员审批" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="940" y="1590" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="joined-org-success" value="成功加入组织" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="1030" y="1720" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="manage-org" value="管理组织 (管理员)" style="whiteSpace=wrap;html=1;rounded=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="260" y="1695" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="org-management-tasks" value="管理成员/部门/角色权限&lt;br&gt;配置组织信息等" style="shape=card;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="440" y="1690" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="arrow-user-start-open-app" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="user-start" target="open-app" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-open-app-action-choice" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="open-app" target="action-choice" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-action-choice-register-phone" value="注册" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="action-choice" target="register-phone" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="250" y="325" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-action-choice-register-email" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="action-choice" target="register-email" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="250" y="325" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="270" y="320" />
              <mxPoint x="270" y="420" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-register-phone-enter-code" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="register-phone" target="enter-phone-code" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-register-email-enter-pass" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="register-email" target="enter-email-pass" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-enter-phone-set-global" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="enter-phone-code" target="set-global-info" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="545" y="320" />
              <mxPoint x="650" y="320" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-enter-email-set-global" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="enter-email-pass" target="set-global-info" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="545" y="420" />
              <mxPoint x="650" y="420" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-set-global-register-success" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="set-global-info" target="register-success" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-action-choice-login" value="登录" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="action-choice" target="login" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="180" y="360" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-login-login-method" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="login" target="login-method" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-login-method-phone-pass" value="手机+密码" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="login-method" target="login-phone-pass" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-login-method-phone-code" value="手机+验证码" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="login-method" target="login-phone-code" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-login-method-email-pass" value="邮箱+密码" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="login-method" target="login-email-pass" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="250" y="655" />
            </Array>
            <mxPoint x="250" y="560" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-phone-pass-enter-cred" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="login-phone-pass" target="enter-credentials" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="530" y="510" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-phone-code-enter-cred" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="login-phone-code" target="enter-credentials" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-email-pass-enter-cred" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="login-email-pass" target="enter-credentials" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="530" y="660" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-enter-cred-verify" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="enter-credentials" target="verify-credentials" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-verify-login-fail" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="verify-credentials" target="login-fail" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-login-fail-yes" value="是" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="login-fail" target="forgot-password" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="820" y="630" />
              <mxPoint x="820" y="630" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-login-fail-no" value="否" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="login-fail" target="login-success" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="880" y="575" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-forgot-pass-login-method" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="forgot-password" target="login-method" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="760" y="680" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="820" y="710" />
              <mxPoint x="180" y="710" />
              <mxPoint x="180" y="570" />
            </Array>
            <mxPoint x="180" y="570" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-register-success-check-multi" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="register-success" target="check-multi-org" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-login-success-check-multi" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="login-success" target="check-multi-org" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1040" y="370" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-check-multi-yes" value="是" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="check-multi-org" target="show-org-list" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-show-org-list-select" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="show-org-list" target="select-org" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-select-org-main-app" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="select-org" target="main-app-interface" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-check-multi-no" value="否 (或单组织)" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="check-multi-org" target="main-app-interface" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1190" y="585" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-app-main-start-select-tab" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="app-main-start" target="select-tab" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-select-tab-chat" value="聊天" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="select-tab" target="chat-tab" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="370" y="800" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-select-tab-friends" value="好友" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="select-tab" target="friends-tab" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-select-tab-my" value="我的" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="select-tab" target="my-tab" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="370" y="970" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-chat-tab-list-action" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="chat-tab" target="chat-list-action" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-chat-list-action-screen" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="chat-list-action" target="chat-screen" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-chat-screen-notif" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="chat-screen" target="new-message-notif" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-friends-tab-list-action" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="friends-tab" target="friends-list-action" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-friends-list-action-choice" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="friends-list-action" target="friend-action-choice" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-friend-action-add" value="添加" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="friend-action-choice" target="add-friend" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-friend-action-view" value="查看资料" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="friend-action-choice" target="view-friend-profile" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1070" y="890" />
              <mxPoint x="1070" y="840" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-view-friend-profile-set-remark" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="view-friend-profile" target="set-remark-delete" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-my-tab-profile-view" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="my-tab" target="my-profile-view" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-my-profile-view-logout" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="my-profile-view" target="logout-action" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="790" y="970" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-logout-action-to-start" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="logout-action" target="open-app" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="890" y="1030" />
              <mxPoint x="60" y="1030" />
              <mxPoint x="60" y="205" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-group-start-action-choice" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="group-start" target="group-action-choice" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-group-action-create" value="创建/管理" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="group-action-choice" target="create-group" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-create-group-invite" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="create-group" target="invite-to-group" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-group-action-join" value="加入/查看" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="group-action-choice" target="join-group" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-join-group-view-info" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="join-group" target="view-group-info" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-view-group-info-settings" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="view-group-info" target="group-settings" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-invite-to-group-chat-screen" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="invite-to-group" target="group-chat-screen" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1000" y="1285" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-view-group-info-to-chat-screen" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="view-group-info" target="group-chat-screen" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="710" y="1285" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-group-settings-to-chat-screen" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="group-settings" target="group-chat-screen" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="880" y="1285" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-org-start-action-choice" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="org-start" target="org-action-choice" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-org-action-create-new" value="创建" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="org-action-choice" target="create-new-org" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-create-new-org-fill-info" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="create-new-org" target="fill-org-info" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-fill-org-info-created" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="fill-org-info" target="org-created" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-org-action-switch" value="切换" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="org-action-choice" target="switch-org" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-switch-org-display-list" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="switch-org" target="display-user-orgs" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-display-list-user-selects" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="display-user-orgs" target="user-selects-org-switch" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-user-selects-context-switched" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="user-selects-org-switch" target="context-switched" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-org-action-join-existing" value="加入" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="org-action-choice" target="join-existing-org" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="360" y="1620" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-join-existing-org-method" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="join-existing-org" target="join-method" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-join-method-approval-needed" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="join-method" target="approval-needed" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-approval-needed-yes" value="是" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="approval-needed" target="admin-approves" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-approval-needed-no" value="否" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="approval-needed" target="joined-org-success" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="835" y="1750" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-admin-approves-joined-success" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="admin-approves" target="joined-org-success" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1090" y="1620" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-org-action-manage" value="管理" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="org-action-choice" target="manage-org" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="330" y="1650" />
              <mxPoint x="330" y="1650" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-manage-org-tasks" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="manage-org" target="org-management-tasks" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="end-main-app" value="返回主界面/继续操作" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1200" y="1130" width="150" height="90" as="geometry" />
        </mxCell>
        <mxCell id="arrow-group-chat-to-end" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.02;entryY=0.633;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="group-chat-screen" target="end-main-app" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="530" y="1360" />
              <mxPoint x="1150" y="1360" />
              <mxPoint x="1150" y="1187" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-new-message-notif-to-end" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="new-message-notif" target="end-main-app" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1380" y="750" />
              <mxPoint x="1380" y="1175" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-add-friend-to-end" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="add-friend" target="end-main-app" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1070" y="1175" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-set-remark-delete-to-end" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="set-remark-delete" target="end-main-app" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1255" y="1130" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="end-org-management" value="返回主界面/继续操作" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="1160" y="1580" width="150" height="90" as="geometry" />
        </mxCell>
        <mxCell id="arrow-org-created-to-end" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="org-created" target="end-org-management" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1220" y="1415" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-context-switched-to-end" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="context-switched" target="end-org-management" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-joined-org-success-to-end" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="joined-org-success" target="end-org-management" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-org-management-tasks-to-end" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="org-management-tasks" target="end-org-management" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="520" y="1810" />
              <mxPoint x="1235" y="1810" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
