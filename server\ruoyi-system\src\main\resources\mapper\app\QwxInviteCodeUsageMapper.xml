<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.QwxInviteCodeUsageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.QwxInviteCodeUsage">
        <id column="usage_id" property="usageId" />
        <result column="code_id" property="codeId" />
        <result column="tenant_id" property="tenantId" />
        <result column="invite_code" property="inviteCode" />
        <result column="user_id" property="userId" />
        <result column="global_user_id" property="globalUserId" />
        <result column="use_time" property="useTime" />
        <result column="ip_address" property="ipAddress" />
        <result column="device_info" property="deviceInfo" />
        <result column="status" property="status" />
        <result column="error_msg" property="errorMsg" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        usage_id, code_id, tenant_id, invite_code, user_id, global_user_id, use_time, ip_address, device_info, status, error_msg, create_time
    </sql>

</mapper>
