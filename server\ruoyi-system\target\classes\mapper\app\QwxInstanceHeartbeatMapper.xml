<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.QwxInstanceHeartbeatMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.QwxInstanceHeartbeat">
        <id column="id" property="id" />
        <result column="instance_id" property="instanceId" />
        <result column="heartbeat_time" property="heartbeatTime" />
        <result column="ip_address" property="ipAddress" />
        <result column="system_info" property="systemInfo" />
        <result column="cpu_usage" property="cpuUsage" />
        <result column="memory_usage" property="memoryUsage" />
        <result column="disk_usage" property="diskUsage" />
        <result column="network_speed" property="networkSpeed" />
        <result column="active_users" property="activeUsers" />
        <result column="online_status" property="onlineStatus" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, instance_id, heartbeat_time, ip_address, system_info, cpu_usage, memory_usage, disk_usage, network_speed, active_users, online_status, create_time
    </sql>

</mapper>
