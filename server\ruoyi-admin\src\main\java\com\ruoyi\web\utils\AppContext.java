package com.ruoyi.web.utils;

import com.ruoyi.app.domain.QwxGlobalUser;
import com.ruoyi.app.model.vo.AppUserTenantIMVo;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * APP上下文工具类
 * <AUTHOR>
 * @date 2025/5/12 上午12:00
 */
public class AppContext {

    /**
     * 获取当前登录的APP用户
     * 
     * @return APP用户信息
     */
    public static QwxGlobalUser getAppLoginUser() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        return (QwxGlobalUser) request.getAttribute("appUser");
    }
    
    /**
     * 获取当前登录用户ID
     * 
     * @return 用户ID
     */
    public static String getGlobalUserId() {
        QwxGlobalUser user = getAppLoginUser();
        return user != null ? user.getGlobalUserId() : null;
    }
    
    /**
     * 获取当前登录用户名
     * 
     * @return 用户名
     */
    public static String getUserName() {
        QwxGlobalUser user = getAppLoginUser();
        return user != null ? user.getUserName() : null;
    }
    
    /**
     * 获取当前租户信息
     * 
     * @return 租户信息
     */
    public static AppUserTenantIMVo.TenantIMInfo getCurrentTenant() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        return (AppUserTenantIMVo.TenantIMInfo) request.getAttribute("tenantInfo");
    }
    
    /**
     * 获取当前租户ID
     * 
     * @return 租户ID
     */
    public static Long getCurrentTenantId() {
        AppUserTenantIMVo.TenantIMInfo tenantInfo = getCurrentTenant();
        return tenantInfo != null ? tenantInfo.getTenantId() : null;
    }
    
    /**
     * 获取当前租户名称
     * 
     * @return 租户名称
     */
    public static String getCurrentTenantName() {
        AppUserTenantIMVo.TenantIMInfo tenantInfo = getCurrentTenant();
        return tenantInfo != null ? tenantInfo.getTenantName() : null;
    }
    
    /**
     * 获取当前云信账号ID
     * 
     * @return 云信账号ID
     */
    public static String getCurrentYxAccountId() {
        AppUserTenantIMVo.TenantIMInfo tenantInfo = getCurrentTenant();
        return tenantInfo != null ? tenantInfo.getYxAccountId() : null;
    }
    
    /**
     * 获取租户内用户ID
     * 
     * @return 租户内用户ID
     */
    public static Long getCurrentTenantUserId() {
        AppUserTenantIMVo.TenantIMInfo tenantInfo = getCurrentTenant();
        return tenantInfo != null ? tenantInfo.getUserId() : null;
    }
    
    /**
     * 判断当前是否使用的是租户令牌
     * 
     * @return 是否使用租户令牌
     */
    public static boolean isTenantToken() {
        return getCurrentTenant() != null;
    }
    
    /**
     * 获取当前租户状态
     * 
     * @return 租户状态（0正常 1停用）
     */
    public static String getCurrentTenantStatus() {
        AppUserTenantIMVo.TenantIMInfo tenantInfo = getCurrentTenant();
        return tenantInfo != null ? tenantInfo.getTenantStatus() : null;
    }
    
    /**
     * 获取当前用户昵称
     * 
     * @return 用户昵称
     */
    public static String getNickName() {
        AppUserTenantIMVo.TenantIMInfo tenantInfo = getCurrentTenant();
        return tenantInfo != null ? tenantInfo.getNickName() : null;
    }
    
    /**
     * 获取云信Token
     * 
     * @return 云信Token
     */
    public static String getYxToken() {
        AppUserTenantIMVo.TenantIMInfo tenantInfo = getCurrentTenant();
        return tenantInfo != null ? tenantInfo.getYxToken() : null;
    }
    
    /**
     * 获取当前用户基本信息（包含租户信息）
     * 
     * @return 用户基本信息Map
     */
    public static Map<String, Object> getCurrentUserInfo() {
        Map<String, Object> userInfo = new HashMap<>();
        
        QwxGlobalUser user = getAppLoginUser();
        if (user != null) {
            userInfo.put("globalUserId", user.getGlobalUserId());
            userInfo.put("userName", user.getUserName());
            userInfo.put("sex", user.getSex());
            userInfo.put("phone", user.getPhone());
            userInfo.put("email", user.getEmail());
            userInfo.put("isPerfectInfo", user.getIsPerfectInfo());
        }
        
        AppUserTenantIMVo.TenantIMInfo tenantInfo = getCurrentTenant();
        if (tenantInfo != null) {
            userInfo.put("tenantId", tenantInfo.getTenantId());
            userInfo.put("tenantName", tenantInfo.getTenantName());
            userInfo.put("tenantUserId", tenantInfo.getUserId());
            userInfo.put("yxAccountId", tenantInfo.getYxAccountId());
            userInfo.put("yxToken", tenantInfo.getYxToken());
            userInfo.put("nickName", tenantInfo.getNickName());
            userInfo.put("avatar", tenantInfo.getAvatar());
        }
        
        return userInfo;
    }
} 