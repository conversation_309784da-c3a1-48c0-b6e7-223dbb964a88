-- ----------------------------
-- 更新用户表结构，适应多租户和单一账号登录多组织
-- ----------------------------

-- 为sys_user表添加字段，用于支持多租户和全局用户功能
ALTER TABLE sys_user ADD COLUMN global_user_id varchar(64) DEFAULT NULL COMMENT '全局用户ID，用于关联全局用户';
ALTER TABLE sys_user ADD COLUMN register_type varchar(10) DEFAULT NULL COMMENT '注册类型（phone手机号 email邮箱）';
ALTER TABLE sys_user ADD COLUMN last_tenant_id bigint(20) DEFAULT NULL COMMENT '上次登录的租户ID';
ALTER TABLE sys_user ADD COLUMN is_global_admin char(1) DEFAULT '0' COMMENT '是否为全局管理员（0否 1是）';
ALTER TABLE sys_user ADD COLUMN wx_openid varchar(64) DEFAULT NULL COMMENT '微信openid';
ALTER TABLE sys_user ADD COLUMN wx_unionid varchar(64) DEFAULT NULL COMMENT '微信unionid';
