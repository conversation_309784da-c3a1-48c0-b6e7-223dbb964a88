package com.ruoyi.web.controller.app;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.web.service.ClientStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket管理控制器
 * 
 * <AUTHOR>
 */
// @Api(tags = "WebSocket管理")
// @RestController
@RequestMapping("/app/ws")
public class AppWebSocketController {

    @Resource
    private ClientStatusService clientStatusService;

    /**
     * 获取在线状态
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 在线状态
     */
    @ApiOperation("获取在线状态")
    @GetMapping("/status/{tenantId}/{userId}")
    public R<Map<String, Object>> getStatus(
            @PathVariable("tenantId") String tenantId,
            @PathVariable("userId") String userId) {
        boolean online = AppWebSocketServer.isUserOnline(tenantId, userId);
        
        Map<String, Object> data = new HashMap<>();
        data.put("tenantId", tenantId);
        data.put("userId", userId);
        data.put("online", online);
        
        return R.ok(data);
    }

    /**
     * 获取在线人数
     * 
     * @return 在线人数
     */
    @ApiOperation("获取在线人数")
    @GetMapping("/count")
    public AjaxResult getOnlineCount() {
        return AjaxResult.success("当前在线人数", AppWebSocketServer.getOnlineCount());
    }
    
    /**
     * 获取指定租户的在线人数
     * 
     * @param tenantId 租户ID
     * @return 在线人数
     */
    @ApiOperation("获取指定租户的在线人数")
    @GetMapping("/count/{tenantId}")
    public AjaxResult getOnlineCountByTenant(@PathVariable("tenantId") String tenantId) {
        return AjaxResult.success("当前租户在线人数", clientStatusService.getOnlineCountByTenant(tenantId));
    }

    /**
     * 向指定用户发送消息
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param message 消息内容
     * @return 操作结果
     */
    @ApiOperation("向指定用户发送消息")
    @PostMapping("/send/{tenantId}/{userId}")
    public AjaxResult sendToUser(
            @PathVariable("tenantId") String tenantId,
            @PathVariable("userId") String userId,
            @RequestParam("message") String message) {
        AppWebSocketServer.sendMessageToUser(tenantId, userId, message);
        return AjaxResult.success("发送成功");
    }

    /**
     * 向指定租户的所有用户发送消息
     * 
     * @param tenantId 租户ID
     * @param message 消息内容
     * @return 操作结果
     */
    @ApiOperation("向指定租户的所有用户发送消息")
    @PostMapping("/sendTenant/{tenantId}")
    public AjaxResult sendToTenant(
            @PathVariable("tenantId") String tenantId,
            @RequestParam("message") String message) {
        AppWebSocketServer.sendMessageToTenant(tenantId, message);
        return AjaxResult.success("发送成功");
    }

    /**
     * 向所有在线用户发送消息
     * 
     * @param message 消息内容
     * @return 操作结果
     */
    @ApiOperation("向所有在线用户发送消息")
    @PostMapping("/sendAll")
    public AjaxResult sendToAll(@RequestParam("message") String message) {
        AppWebSocketServer.sendMessageToAll(message);
        return AjaxResult.success("发送成功");
    }
} 