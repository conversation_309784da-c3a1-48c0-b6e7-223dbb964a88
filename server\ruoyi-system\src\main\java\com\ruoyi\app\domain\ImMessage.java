package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 消息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ImMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    @TableId(value = "msg_id", type = IdType.AUTO)
    private Long msgId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 消息类型（0单聊 1群聊）
     */
    private String msgType;

    /**
     * 内容类型（00文本 01图片 02语音 03视频 04文件）
     */
    private String contentType;

    /**
     * 发送者ID
     */
    private Long fromId;

    /**
     * 接收者ID（用户ID或群组ID）
     */
    private Long toId;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 状态（0未读 1已读 2撤回 3删除）
     */
    private String status;

    /**
     * 是否撤回（0否 1是）
     */
    private String isRecall;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 云信消息ID
     */
    private String yxMsgId;

    /**
     * 服务端扩展字段
     */
    private String remoteExtension;

    /**
     * 客户端扩展字段
     */
    private String localExtension;

    /**
     * 附件信息
     */
    private String attachmentInfo;

    /**
     * 消息方向
     */
    private String msgDirection;

    /**
     * 毫秒级时间戳
     */
    private Long timeMs;


}
