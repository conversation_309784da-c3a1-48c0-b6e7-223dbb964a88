<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.ImMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.ImMessage">
        <id column="msg_id" property="msgId" />
        <result column="tenant_id" property="tenantId" />
        <result column="msg_type" property="msgType" />
        <result column="content_type" property="contentType" />
        <result column="from_id" property="fromId" />
        <result column="to_id" property="toId" />
        <result column="content" property="content" />
        <result column="send_time" property="sendTime" />
        <result column="status" property="status" />
        <result column="is_recall" property="isRecall" />
        <result column="create_time" property="createTime" />
        <result column="yx_msg_id" property="yxMsgId" />
        <result column="remote_extension" property="remoteExtension" />
        <result column="local_extension" property="localExtension" />
        <result column="attachment_info" property="attachmentInfo" />
        <result column="msg_direction" property="msgDirection" />
        <result column="time_ms" property="timeMs" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        msg_id, tenant_id, msg_type, content_type, from_id, to_id, content, send_time, status, is_recall, create_time, yx_msg_id, remote_extension, local_extension, attachment_info, msg_direction, time_ms
    </sql>

</mapper>
