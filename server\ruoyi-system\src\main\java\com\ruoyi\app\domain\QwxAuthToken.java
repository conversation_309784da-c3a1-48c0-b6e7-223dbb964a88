package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 授权Token表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QwxAuthToken implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Token ID
     */
    @TableId(value = "token_id", type = IdType.AUTO)
    private Long tokenId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 授权Token
     */
    private String token;

    /**
     * 签发时间
     */
    private Date issueTime;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 状态（0有效 1无效）
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 部署实例ID，关联qwx_deploy_instance表
     */
    private Long instanceId;

    /**
     * 获取Token的公网IP
     */
    private String publicIp;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 最后验证时间
     */
    private Date lastVerifyTime;


}
