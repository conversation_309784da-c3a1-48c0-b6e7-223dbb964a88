package com.ruoyi.web.service;

import com.ruoyi.web.config.EmailConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.util.Properties;

/**
 * 邮件服务接口实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class EmailService {
    

    @Resource
    private EmailConfig emailConfig;
    
    /**
     * 发送邮箱验证码
     * 
     * @param email 邮箱地址
     * @param code 验证码
     * @return 发送结果
     */
    public boolean sendEmailCode(String email, String code) {
        // 检查是否启用邮件功能
        if (!emailConfig.isEnabled()) {
            log.info("邮件功能未启用，模拟发送验证码到邮箱: {}, 验证码: {}", email, code);
            return true;
        }
        
        log.info("向邮箱 [{}] 发送验证码: {}", email, code);
        
        try {
            JavaMailSender mailSender = createMailSender();
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom(emailConfig.getFrom());
            helper.setTo(email);
            helper.setSubject(emailConfig.getSubject());
            
            // 填充邮件内容模板
            String content = String.format(emailConfig.getTemplate(), code, 5); // 5分钟有效期
            helper.setText(content, true);
            
            // 发送邮件
            mailSender.send(message);
            log.info("邮件验证码发送成功");
            return true;
        } catch (Exception e) {
            log.error("邮件验证码发送失败：", e);
            return false;
        }
    }
    
    /**
     * 创建邮件发送器
     * 
     * @return JavaMailSender
     */
    private JavaMailSender createMailSender() {
        JavaMailSenderImpl sender = new JavaMailSenderImpl();
        sender.setHost(emailConfig.getHost());
        sender.setPort(emailConfig.getPort());
        sender.setUsername(emailConfig.getUsername());
        sender.setPassword(emailConfig.getPassword());
        sender.setDefaultEncoding("UTF-8");
        
        Properties props = new Properties();
        props.setProperty("mail.smtp.timeout", String.valueOf(emailConfig.getTimeout() * 1000));
        
        if (emailConfig.isSsl()) {
            props.setProperty("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
            props.setProperty("mail.smtp.socketFactory.fallback", "false");
            props.setProperty("mail.smtp.socketFactory.port", String.valueOf(emailConfig.getPort()));
            props.setProperty("mail.smtp.ssl.enable", "true");
        }
        
        props.setProperty("mail.smtp.auth", "true");
        props.setProperty("mail.smtp.starttls.enable", emailConfig.isSsl() ? "true" : "false");
        
        sender.setJavaMailProperties(props);
        return sender;
    }
} 