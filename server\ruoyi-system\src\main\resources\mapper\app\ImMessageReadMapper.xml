<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.ImMessageReadMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.ImMessageRead">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="msg_id" property="msgId" />
        <result column="user_id" property="userId" />
        <result column="read_time" property="readTime" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, msg_id, user_id, read_time, create_time
    </sql>

</mapper>
