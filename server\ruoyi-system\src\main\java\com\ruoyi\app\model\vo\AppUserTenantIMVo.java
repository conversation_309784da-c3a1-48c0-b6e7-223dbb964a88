package com.ruoyi.app.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户租户和云信账号组合信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Data
@ApiModel(value = "用户组织与网易云信信息")
public class AppUserTenantIMVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组织账号列表
     */
    @ApiModelProperty(value = "组织账号列表")
    private List<TenantIMInfo> tenantInfoList;

    /**
     * 内部组合类
     */
    @Data
    public static class TenantIMInfo implements Serializable {

        private static final long serialVersionUID = 1L;
        /**
         * 请求token
         */
        @ApiModelProperty(value = "请求token")
        private String token;
        // ========== 租户信息字段 ==========
        /**
         * 租户ID
         */
        @ApiModelProperty(value = "租户ID")
        private Long tenantId;

        /**
         * 租户名称
         */
        @ApiModelProperty(value = "租户名称")
        private String tenantName;

        /**
         * 租户编码，唯一标识
         */
        @ApiModelProperty(value = "租户编码")
        private String tenantCode;

        /**
         * 企业logo
         */
        @ApiModelProperty(value = "企业logo")
        private String logo;

        /**
         * 行业
         */
        @ApiModelProperty(value = "行业")
        private String industry;

        /**
         * 企业规模
         */
        @ApiModelProperty(value = "企业规模")
        private String scale;

        /**
         * 企业地址
         */
        @ApiModelProperty(value = "企业地址")
        private String address;

        /**
         * 联系人
         */
        @ApiModelProperty(value = "联系人")
        private String contactUser;

        /**
         * 联系电话
         */
        @ApiModelProperty(value = "联系电话")
        private String contactPhone;

        /**
         * 域名
         */
        @ApiModelProperty(value = "域名")
        private String domain;

        /**
         * 企业介绍
         */
        @ApiModelProperty(value = "企业介绍")
        private String intro;

        /**
         * 租户状态（0正常 1停用）
         */
        @ApiModelProperty(value = "租户状态（0正常 1停用）")
        private String tenantStatus;

        /**
         * 过期时间
         */
        @ApiModelProperty(value = "过期时间")
        private Date expireTime;

        /**
         * 账号数量
         */
        @ApiModelProperty(value = "账号数量")
        private Integer accountCount;

        // ========== 用户在该组织的用户信息映射字段 ==========
        /**
         * 是否是管理员(0否 1是)
         */
        @ApiModelProperty(value = "是否是管理员(0否 1是)")
        private String isAdmin;
        /**
         * 用户昵称
         */
        @ApiModelProperty(value = "用户昵称")
        private String nickName;

        /**
         * 头像地址
         */
        @ApiModelProperty(value = "头像地址")
        private String avatar;
        /**
         * 职位
         */
        @ApiModelProperty(value = "职位")
        private String position;

        /**
         * 加入时间
         */
        @ApiModelProperty(value = "加入时间")
        private Date joinTime;

        /**
         * 是否已退出
         */
        @ApiModelProperty(value = "是否已退出")
        private String isQuit;


        // ========== 云信账号映射字段 ==========
        /**
         * 云信账号映射ID
         */
        @ApiModelProperty(value = "云信账号映射ID")
        private Long imMappingId;

        /**
         * 系统用户ID
         */
        @ApiModelProperty(value = "系统用户ID")
        private Long userId;

        /**
         * 全局用户ID
         */
        @ApiModelProperty(value = "全局用户ID")
        private String globalUserId;

        /**
         * 云信账号ID
         */
        @ApiModelProperty(value = "云信账号ID")
        private String yxAccountId;

        /**
         * 云信Token
         */
        @ApiModelProperty(value = "云信Token")
        private String yxToken;

        /**
         * 云信账号状态（0正常 1停用）
         */
        @ApiModelProperty(value = "云信账号状态（0正常 1停用）")
        private String imStatus;

        /**
         * 创建时间
         */
        @ApiModelProperty(value = "创建时间")
        private Date createTime;

        /**
         * 更新时间
         */
        @ApiModelProperty(value = "更新时间")
        private Date updateTime;
    }
} 