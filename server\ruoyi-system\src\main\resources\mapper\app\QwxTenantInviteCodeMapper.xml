<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.QwxTenantInviteCodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.QwxTenantInviteCode">
        <id column="code_id" property="codeId" />
        <result column="tenant_id" property="tenantId" />
        <result column="invite_code" property="inviteCode" />
        <result column="code_name" property="codeName" />
        <result column="creator_id" property="creatorId" />
        <result column="max_uses" property="maxUses" />
        <result column="used_count" property="usedCount" />
        <result column="expire_time" property="expireTime" />
        <result column="status" property="status" />
        <result column="auto_activate" property="autoActivate" />
        <result column="need_approval" property="needApproval" />
        <result column="default_role" property="defaultRole" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="selectQwxTenantInviteCodeVo">
        select code_id, tenant_id, invite_code, code_name, creator_id, max_uses, used_count, expire_time, status, auto_activate, need_approval, default_role, create_time, update_time, remark from qwx_tenant_invite_code
    </sql>
 <select id="selectQwxTenantInviteCodeList" parameterType="QwxTenantInviteCode" resultMap="BaseResultMap">
        <include refid="selectQwxTenantInviteCodeVo"/>
        <where>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
            <if test="inviteCode != null  and inviteCode != ''"> and invite_code = #{inviteCode}</if>
            <if test="codeName != null  and codeName != ''"> and code_name like concat('%', #{codeName}, '%')</if>
            <if test="creatorId != null "> and creator_id = #{creatorId}</if>
            <if test="maxUses != null "> and max_uses = #{maxUses}</if>
            <if test="usedCount != null "> and used_count = #{usedCount}</if>
            <if test="expireTime != null "> and expire_time = #{expireTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="autoActivate != null  and autoActivate != ''"> and auto_activate = #{autoActivate}</if>
            <if test="needApproval != null  and needApproval != ''"> and need_approval = #{needApproval}</if>
            <if test="defaultRole != null  and defaultRole != ''"> and default_role = #{defaultRole}</if>
        </where>
    </select>

    <select id="selectQwxTenantInviteCodeByCodeId" parameterType="Long" resultMap="BaseResultMap">
        <include refid="selectQwxTenantInviteCodeVo"/>
        where code_id = #{codeId}
    </select>
</mapper>
