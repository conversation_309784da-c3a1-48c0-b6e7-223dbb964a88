<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.QwxAuthTokenMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.QwxAuthToken">
        <id column="token_id" property="tokenId" />
        <result column="tenant_id" property="tenantId" />
        <result column="token" property="token" />
        <result column="issue_time" property="issueTime" />
        <result column="expire_time" property="expireTime" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="instance_id" property="instanceId" />
        <result column="public_ip" property="publicIp" />
        <result column="device_info" property="deviceInfo" />
        <result column="last_verify_time" property="lastVerifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        token_id, tenant_id, token, issue_time, expire_time, status, create_time, update_time, instance_id, public_ip, device_info, last_verify_time
    </sql>

</mapper>
