<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.QwxInstanceConnectionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.QwxInstanceConnection">
        <id column="conn_id" property="connId" />
        <result column="instance_id" property="instanceId" />
        <result column="saas_api_url" property="saasApiUrl" />
        <result column="auth_endpoint" property="authEndpoint" />
        <result column="heartbeat_endpoint" property="heartbeatEndpoint" />
        <result column="sync_endpoint" property="syncEndpoint" />
        <result column="api_key" property="apiKey" />
        <result column="api_secret" property="apiSecret" />
        <result column="conn_timeout" property="connTimeout" />
        <result column="read_timeout" property="readTimeout" />
        <result column="retry_count" property="retryCount" />
        <result column="retry_interval" property="retryInterval" />
        <result column="encryption_type" property="encryptionType" />
        <result column="encryption_key" property="encryptionKey" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        conn_id, instance_id, saas_api_url, auth_endpoint, heartbeat_endpoint, sync_endpoint, api_key, api_secret, conn_timeout, read_timeout, retry_count, retry_interval, encryption_type, encryption_key, status, create_time, update_time
    </sql>

</mapper>
