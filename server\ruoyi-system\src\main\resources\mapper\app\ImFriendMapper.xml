<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.ImFriendMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.ImFriend">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="user_id" property="userId" />
        <result column="friend_id" property="friendId" />
        <result column="remark_name" property="remarkName" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, user_id, friend_id, remark_name, status, create_time, update_time
    </sql>

</mapper>
