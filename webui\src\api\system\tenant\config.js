import request from '@/utils/request'

// 查询租户配置列表
export function listTenantConfig(query) {
  return request({
    url: '/system/tenant/config/list',
    method: 'get',
    params: query
  })
}

// 查询租户配置详细
export function getTenantConfig(configId) {
  return request({
    url: '/system/tenant/config/' + configId,
    method: 'get'
  })
}

// 根据租户ID获取配置列表
export function getConfigByTenantId(tenantId) {
  return request({
    url: '/system/tenant/config/tenant/' + tenantId,
    method: 'get'
  })
}

// 获取配置值
export function getConfigValue(tenantId, configKey) {
  return request({
    url: '/system/tenant/config/value/' + tenantId + '/' + configKey,
    method: 'get'
  })
}

// 新增租户配置
export function addTenantConfig(data) {
  return request({
    url: '/system/tenant/config',
    method: 'post',
    data: data
  })
}

// 修改租户配置
export function updateTenantConfig(data) {
  return request({
    url: '/system/tenant/config',
    method: 'put',
    data: data
  })
}

// 批量更新租户配置
export function updateBatchConfig(tenantId, data) {
  return request({
    url: '/system/tenant/config/batch/' + tenantId,
    method: 'put',
    data: data
  })
}

// 删除租户配置
export function delTenantConfig(configIds) {
  return request({
    url: '/system/tenant/config/' + configIds,
    method: 'delete'
  })
}

// 重置为默认配置
export function resetToDefault(tenantId) {
  return request({
    url: '/system/tenant/config/reset/' + tenantId,
    method: 'put'
  })
} 