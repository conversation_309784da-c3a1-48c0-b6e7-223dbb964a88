package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 群组表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ImGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @TableId(value = "group_id", type = IdType.AUTO)
    private Long groupId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 群组名称
     */
    private String groupName;

    /**
     * 群组头像
     */
    private String groupAvatar;

    /**
     * 群主ID
     */
    private Long ownerId;

    /**
     * 群介绍
     */
    private String introduction;

    /**
     * 群公告
     */
    private String announcement;

    /**
     * 成员数量
     */
    private Integer memberCount;

    /**
     * 最大成员数
     */
    private Integer maxMemberCount;

    /**
     * 加入方式（0自由加入 1需审批 2禁止加入）
     */
    private String joinMode;

    /**
     * 状态（0正常 1解散）
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
