<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.QwxInviteApprovalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.app.domain.QwxInviteApproval">
        <id column="approval_id" property="approvalId" />
        <result column="usage_id" property="usageId" />
        <result column="tenant_id" property="tenantId" />
        <result column="user_id" property="userId" />
        <result column="global_user_id" property="globalUserId" />
        <result column="approver_id" property="approverId" />
        <result column="apply_time" property="applyTime" />
        <result column="approval_time" property="approvalTime" />
        <result column="approval_status" property="approvalStatus" />
        <result column="rejection_reason" property="rejectionReason" />
        <result column="apply_info" property="applyInfo" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        approval_id, usage_id, tenant_id, user_id, global_user_id, approver_id, apply_time, approval_time, approval_status, rejection_reason, apply_info, create_time, update_time
    </sql>

</mapper>
