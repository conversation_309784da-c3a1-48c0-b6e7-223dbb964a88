package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 刷新Token入参
 * <AUTHOR>
 * @date 2025/5/20
 */
@Data
@ApiModel(value = "刷新Token请求参数")
public class RefreshTokenDto {
    
    /**
     * 刷新令牌
     */
    @ApiModelProperty(value = "刷新令牌", required = true, example = "eyJhbGciOiJIUzUxMiJ9...")
    private String refreshToken;
} 