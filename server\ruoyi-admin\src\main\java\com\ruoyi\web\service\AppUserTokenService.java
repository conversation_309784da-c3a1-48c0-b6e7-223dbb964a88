package com.ruoyi.web.service;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.app.domain.QwxGlobalUser;
import com.ruoyi.app.mapper.ImYxaccountMappingMapper;
import com.ruoyi.app.mapper.QwxTenantMapper;
import com.ruoyi.app.mapper.QwxUserTenantMapper;
import com.ruoyi.app.model.vo.AppLoginResultVo;
import com.ruoyi.app.model.vo.AppUserTenantIMVo;
import com.ruoyi.app.service.QwxGlobalUserService;
import com.ruoyi.common.constant.AppRedisConstants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * APP用户Token服务
 *
 * <AUTHOR>
 * @date 2025/5/11 下午10:49
 */
@Slf4j
@Service
public class AppUserTokenService {

    // 令牌自定义标识
    @Value("${app-token.header}")
    private String header;

    // 令牌秘钥
    @Value("${app-token.secret}")
    private String secret;

    // 令牌有效期（默认30天）
    @Value("${app-token.expireTime}")
    private int expireTime;

    // 是否允许多端同时登录
    @Value("${app-token.multiLogin:true}")
    private boolean multiLogin;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private QwxGlobalUserService qwxGlobalUserService;
    @Resource
    private QwxTenantMapper qwxTenantMapper;
    @Resource
    private ImYxaccountMappingMapper imYxaccountMappingMapper;
    @Resource
    private QwxUserTenantMapper qwxUserTenantMapper;

    /**
     * 创建APP用户token（存量接口，兼容老版本）
     *
     * @param user 用户信息
     * @return token信息
     * @deprecated 该方法已不再使用，请使用租户token相关方法
     */
    public Map<String, Object> createToken(QwxGlobalUser user) {
        // 生成token唯一标识
        String uuid = IdUtils.fastUUID();
        String userId = user.getGlobalUserId();

        // 如果不允许多端登录，先清除该用户在Redis中的token
        if (!multiLogin) {
            removeUserToken(userId);
        }

        // 设置用户信息到Redis
        String userKey = getTokenKey(uuid);
        redisTemplate.opsForValue()
                .set(userKey, user, expireTime, TimeUnit.MINUTES);

        // 创建JWT accessToken
        Map<String, Object> claims = new HashMap<>();
        claims.put("uuid", uuid);
        claims.put("userId", userId);
        claims.put("userName", user.getUserName());

        String accessToken = createJWT(claims);
        
        // 创建refreshToken（有效期是accessToken的2倍）
        String refreshUuid = IdUtils.fastUUID();
        String refreshUserKey = getRefreshTokenKey(refreshUuid);
        
        // 存储refreshToken关联的信息（包含对应的accessToken的uuid）
        Map<String, Object> refreshData = new HashMap<>();
        refreshData.put("uuid", uuid);
        refreshData.put("userId", userId);
        refreshData.put("userName", user.getUserName());
        
        // 存储到Redis，有效期是accessToken的2倍
        redisTemplate.opsForValue()
                .set(refreshUserKey, refreshData, expireTime * 2, TimeUnit.MINUTES);
                
        // 创建refreshToken的JWT
        Map<String, Object> refreshClaims = new HashMap<>();
        refreshClaims.put("refreshUuid", refreshUuid);
        refreshClaims.put("userId", userId);
        refreshClaims.put("type", "refresh");
        
        String refreshToken = createJWT(refreshClaims);

        // 返回token信息
        Map<String, Object> tokenInfo = new HashMap<>();
        tokenInfo.put("access_token", accessToken);
        tokenInfo.put("refresh_token", refreshToken);
        tokenInfo.put("expires_in", expireTime * 60); // 转换为秒

        return tokenInfo;
    }

    /**
     * 创建租户组织对应的token
     *
     * @param user       用户信息
     * @param tenantInfo 租户和云信账号信息
     * @return token信息
     */
    public Map<String, Object> createTenantToken(QwxGlobalUser user, AppUserTenantIMVo.TenantIMInfo tenantInfo) {
        // 生成token唯一标识
        String uuid = IdUtils.fastUUID();
        String userId = user.getGlobalUserId();
        Long tenantId = tenantInfo.getTenantId();
        String yxAccountId = tenantInfo.getYxAccountId();

        // 组装要保存到Redis的信息（用户+租户信息）
        Map<String, Object> tokenInfo = new HashMap<>();
        tokenInfo.put("user", user);
        tokenInfo.put("tenantInfo", tenantInfo);

        // 设置token信息到Redis
        String tokenKey = getTenantTokenKey(uuid);
        redisTemplate.opsForValue()
                .set(tokenKey, tokenInfo, expireTime, TimeUnit.MINUTES);

        // 创建JWT accessToken，包含租户ID和云信账号ID
        Map<String, Object> claims = new HashMap<>();
        claims.put("uuid", uuid);
        claims.put("userId", userId);
        claims.put("userName", user.getUserName());
        claims.put("tenantId", tenantId);
        claims.put("tenantName", tenantInfo.getTenantName());
        claims.put("yxAccountId", yxAccountId);

        String accessToken = createJWT(claims);

        // 返回token信息
        Map<String, Object> result = new HashMap<>();
        result.put("access_token", accessToken);
        result.put("expires_in", expireTime * 60); // 转换为秒

        return result;
    }

    /**
     * 创建JWT
     *
     * @param claims 数据声明
     * @return JWT
     */
    private String createJWT(Map<String, Object> claims) {
        long nowMillis = System.currentTimeMillis();

        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(new java.util.Date(nowMillis))
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }

    /**
     * 从请求中获取用户信息
     *
     * @param request 请求
     * @return 用户信息
     */
    public QwxGlobalUser getAppLoginUser(HttpServletRequest request) {
        // 获取请求头中的token
        String token = getToken(request);
        if (StringUtils.hasText(token)) {
            try {
                // 解析token
                Claims claims = parseToken(token);
                String uuid = (String) claims.get("uuid");

                // 判断是否有租户ID，如果有则是新版租户token
                if (claims.get("tenantId") != null) {
                    String tokenKey = getTenantTokenKey(uuid);
                    Map<String, Object> tokenInfo = (Map<String, Object>) redisTemplate.opsForValue()
                            .get(tokenKey);
                    if (tokenInfo != null) {
                        // 使用fastjson2正确转换为QwxGlobalUser对象
                        Object userObj = tokenInfo.get("user");
                        if (userObj != null) {
                            if (userObj instanceof QwxGlobalUser) {
                                return (QwxGlobalUser) userObj;
                            } else {
                                // 通过JSON转换处理
                                return JSON.parseObject(
                                        JSON.toJSONString(userObj),
                                        QwxGlobalUser.class
                                );
                            }
                        }
                    }
                } else {
                    // 原有逻辑，兼容老版本
                    String userKey = getTokenKey(uuid);
                    Object userObj = redisTemplate.opsForValue()
                            .get(userKey);
                    if (userObj != null) {
                        if (userObj instanceof QwxGlobalUser) {
                            return (QwxGlobalUser) userObj;
                        } else {
                            // 通过JSON转换处理
                            return JSON.parseObject(
                                    JSON.toJSONString(userObj),
                                    QwxGlobalUser.class
                            );
                        }
                    }
                }
            } catch (Exception e) {
                log.error("获取用户信息异常'{}'", e.getMessage());
            }
        }
        return null;
    }

    /**
     * 从请求中获取租户信息
     *
     * @param request 请求
     * @return 租户信息，如果不是租户token则返回null
     */
    public AppUserTenantIMVo.TenantIMInfo getTenantInfo(HttpServletRequest request) {
        // 获取请求头中的token
        String token = getToken(request);
        if (StringUtils.hasText(token)) {
            try {
                // 解析token
                Claims claims = parseToken(token);
                // 检查是否包含租户ID
                if (claims.get("tenantId") != null) {
                    String uuid = (String) claims.get("uuid");
                    String tokenKey = getTenantTokenKey(uuid);
                    Map<String, Object> tokenInfo = (Map<String, Object>) redisTemplate.opsForValue()
                            .get(tokenKey);
                    if (tokenInfo != null) {
                        // 使用fastjson2正确转换为TenantIMInfo对象
                        Object tenantInfoObj = tokenInfo.get("tenantInfo");
                        if (tenantInfoObj != null) {
                            if (tenantInfoObj instanceof AppUserTenantIMVo.TenantIMInfo) {
                                return (AppUserTenantIMVo.TenantIMInfo) tenantInfoObj;
                            } else {
                                // 通过JSON转换处理
                                return JSON.parseObject(
                                        JSON.toJSONString(tenantInfoObj),
                                        AppUserTenantIMVo.TenantIMInfo.class
                                );
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("获取租户信息异常'{}'", e.getMessage());
            }
        }
        return null;
    }

    /**
     * 从token中获取租户ID
     *
     * @param token JWT token
     * @return 租户ID，如果不包含则返回null
     */
    public Long getTenantIdFromToken(String token) {
        if (StringUtils.hasText(token)) {
            try {
                Claims claims = parseToken(token);
                Object tenantId = claims.get("tenantId");
                if (tenantId != null) {
                    // 根据实际情况可能需要类型转换
                    if (tenantId instanceof Integer) {
                        return ((Integer) tenantId).longValue();
                    } else if (tenantId instanceof Long) {
                        return (Long) tenantId;
                    } else {
                        return Long.valueOf(tenantId.toString());
                    }
                }
            } catch (Exception e) {
                log.error("从token获取租户ID异常'{}'", e.getMessage());
            }
        }
        return null;
    }

    /**
     * 从token中获取云信账号ID
     *
     * @param token JWT token
     * @return 云信账号ID，如果不包含则返回null
     */
    public String getYxAccountIdFromToken(String token) {
        if (StringUtils.hasText(token)) {
            try {
                Claims claims = parseToken(token);
                return (String) claims.get("yxAccountId");
            } catch (Exception e) {
                log.error("从token获取云信账号ID异常'{}'", e.getMessage());
            }
        }
        return null;
    }

    /**
     * 验证令牌有效期
     *
     * @param user 用户信息
     */
    public void verifyToken(QwxGlobalUser user, String uuid) {
        // 续期Token
        String userKey = getTokenKey(uuid);
        redisTemplate.expire(userKey, expireTime, TimeUnit.MINUTES);
    }

    /**
     * 验证租户令牌有效期
     *
     * @param uuid 令牌唯一标识
     */
    public void verifyTenantToken(String uuid) {
        // 续期租户Token
        String tokenKey = getTenantTokenKey(uuid);
        redisTemplate.expire(tokenKey, expireTime, TimeUnit.MINUTES);
    }

    /**
     * 删除用户令牌（兼容旧版本）
     *
     * @param userId 用户ID
     */
    public void removeUserToken(String userId) {
        // 遍历查找用户的所有token
        // 注意：这种方式在实际生产环境中可能不够高效，可以考虑使用更好的索引方式
        String pattern = AppRedisConstants.APP_TOKEN_PREFIX + "*";
        for (Object key : redisTemplate.keys(pattern)) {
            String keyStr = (String) key;
            Object userObj = redisTemplate.opsForValue()
                    .get(keyStr);
            if (userObj != null) {
                QwxGlobalUser user = null;

                // 类型转换处理
                if (userObj instanceof QwxGlobalUser) {
                    user = (QwxGlobalUser) userObj;
                } else {
                    try {
                        // 通过JSON转换处理
                        user = JSON.parseObject(
                                JSON.toJSONString(userObj),
                                QwxGlobalUser.class
                        );
                    } catch (Exception e) {
                        log.error("用户信息转换异常：{}", e.getMessage());
                        continue;
                    }
                }

                if (user != null && userId.equals(user.getGlobalUserId())) {
                    redisTemplate.delete(keyStr);
                }
            }
        }
    }

    /**
     * 删除用户的所有租户令牌
     *
     * @param userId 用户ID
     */
    public void removeUserTenantTokens(String userId) {
        // 遍历查找用户的所有租户token
        String pattern = AppRedisConstants.APP_TENANT_TOKEN_PREFIX + "*";
        for (Object key : redisTemplate.keys(pattern)) {
            String keyStr = (String) key;
            Map<String, Object> tokenInfo = (Map<String, Object>) redisTemplate.opsForValue()
                    .get(keyStr);
            if (tokenInfo != null && tokenInfo.get("user") != null) {
                Object userObj = tokenInfo.get("user");
                QwxGlobalUser user = null;

                // 类型转换处理
                if (userObj instanceof QwxGlobalUser) {
                    user = (QwxGlobalUser) userObj;
                } else {
                    try {
                        // 通过JSON转换处理
                        user = JSON.parseObject(
                                JSON.toJSONString(userObj),
                                QwxGlobalUser.class
                        );
                    } catch (Exception e) {
                        log.error("用户信息转换异常：{}", e.getMessage());
                        continue;
                    }
                }

                if (user != null && userId.equals(user.getGlobalUserId())) {
                    redisTemplate.delete(keyStr);
                }
            }
        }
    }

    /**
     * 注销租户登录
     *
     * @param token JWT token
     * @return 结果
     */
    public boolean logoutTenant(String token) {
        try {
            Claims claims = parseToken(token);
            String uuid = (String) claims.get("uuid");
            Long tenantId = null;

            // 获取租户ID
            Object tenantIdObj = claims.get("tenantId");
            if (tenantIdObj != null) {
                if (tenantIdObj instanceof Integer) {
                    tenantId = ((Integer) tenantIdObj).longValue();
                } else if (tenantIdObj instanceof Long) {
                    tenantId = (Long) tenantIdObj;
                } else {
                    tenantId = Long.valueOf(tenantIdObj.toString());
                }
            }

            // 删除Redis中的租户token信息
            String tokenKey = getTenantTokenKey(uuid);
            redisTemplate.delete(tokenKey);

            // 将token加入黑名单
            String blacklistKey = getTenantBlacklistKey(uuid);
            redisTemplate.opsForValue()
                    .set(blacklistKey, tenantId, expireTime, TimeUnit.MINUTES);

            return true;
        } catch (Exception e) {
            log.error("注销租户登录异常'{}'", e.getMessage());
            return false;
        }
    }

    /**
     * 校验令牌是否有效（兼容旧版本）
     *
     * @param token 令牌
     * @return 结果
     * @deprecated 该方法已不再使用，请使用checkTenantToken方法
     */
    public boolean checkToken(String token) {
        try {
            Claims claims = parseToken(token);
            String uuid = (String) claims.get("uuid");

            // 检查是否在黑名单中
            String blacklistKey = getBlacklistKey(uuid);
            if (Boolean.TRUE.equals(redisTemplate.hasKey(blacklistKey))) {
                return false;
            }

            // 检查用户信息是否存在
            String userKey = getTokenKey(uuid);
            return redisTemplate.hasKey(userKey);
        } catch (Exception e) {
            log.error("校验令牌异常'{}'", e.getMessage());
            return false;
        }
    }

    /**
     * 校验租户令牌是否有效
     *
     * @param token 令牌
     * @return 结果
     */
    public boolean checkTenantToken(String token) {
        try {
            Claims claims = parseToken(token);
            String uuid = (String) claims.get("uuid");

            // 检查是否有租户ID
            if (claims.get("tenantId") == null) {
                return false;
            }

            // 检查是否在黑名单中
            String blacklistKey = getTenantBlacklistKey(uuid);
            if (Boolean.TRUE.equals(redisTemplate.hasKey(blacklistKey))) {
                return false;
            }

            // 检查租户token信息是否存在
            String tokenKey = getTenantTokenKey(uuid);
            return redisTemplate.hasKey(tokenKey);
        } catch (Exception e) {
            log.error("校验租户令牌异常'{}'", e.getMessage());
            return false;
        }
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    private Claims parseToken(String token) {
        return Jwts.parser()
                .setSigningKey(secret)
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 从过期的token中解析用户ID
     * 此方法不验证token是否过期，只验证签名
     *
     * @param token 可能已过期的token
     * @return 用户ID，如果解析失败则返回null
     */
    public String getUserIdFromToken(String token) {
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        
        try {
            Claims claims = Jwts.parser()
                    .setSigningKey(secret)
                    .parseClaimsJws(token)
                    .getBody();
            
            return (String) claims.get("userId");
        } catch (Exception e) {
            log.error("从token获取用户ID异常: {}", e.getMessage());
            return null;
        }
    }


    /**
     * 从请求中获取token
     *
     * @param request 请求
     * @return token
     */
    public String getToken(HttpServletRequest request) {
        return request.getHeader(header);
    }

    /**
     * 获取token在Redis中的key
     *
     * @param uuid 唯一标识
     * @return token key
     */
    private String getTokenKey(String uuid) {
        return AppRedisConstants.APP_TOKEN_PREFIX + uuid;
    }

    /**
     * 获取租户token在Redis中的key
     *
     * @param uuid 唯一标识
     * @return tenant token key
     */
    private String getTenantTokenKey(String uuid) {
        return AppRedisConstants.APP_TENANT_TOKEN_PREFIX + uuid;
    }

    /**
     * 获取黑名单token在Redis中的key
     *
     * @param uuid 唯一标识
     * @return blacklist key
     */
    private String getBlacklistKey(String uuid) {
        return AppRedisConstants.APP_TOKEN_BLACKLIST + uuid;
    }

    /**
     * 获取租户黑名单token在Redis中的key
     *
     * @param uuid 唯一标识
     * @return tenant blacklist key
     */
    private String getTenantBlacklistKey(String uuid) {
        return AppRedisConstants.APP_TENANT_TOKEN_BLACKLIST + uuid;
    }

    /**
     * 获取refreshToken在Redis中的key
     *
     * @param refreshUuid 刷新token的唯一标识
     * @return refresh token key
     */
    private String getRefreshTokenKey(String refreshUuid) {
        return AppRedisConstants.APP_TOKEN_PREFIX + "refresh_" + refreshUuid;
    }

    /**
     * 验证刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 如果有效返回关联的userId，否则返回null
     */
    public String verifyRefreshToken(String refreshToken) {
        try {
            Claims claims = parseToken(refreshToken);
            
            // 检查是否是refresh类型的token
            if (!"refresh".equals(claims.get("type"))) {
                log.error("不是有效的刷新令牌");
                return null;
            }
            
            String refreshUuid = (String) claims.get("refreshUuid");
            String userId = (String) claims.get("userId");
            
            if (StringUtils.isEmpty(refreshUuid) || StringUtils.isEmpty(userId)) {
                return null;
            }
            
            // 检查Redis中是否存在该refreshToken
            String refreshKey = getRefreshTokenKey(refreshUuid);
            Map<String, Object> refreshData = (Map<String, Object>) redisTemplate.opsForValue().get(refreshKey);
            
            if (refreshData == null) {
                log.error("刷新令牌已过期或不存在");
                return null;
            }
            
            // 验证userId是否匹配
            String storedUserId = (String) refreshData.get("userId");
            if (!userId.equals(storedUserId)) {
                log.error("刷新令牌中的用户ID不匹配");
                return null;
            }
            
            return userId;
        } catch (Exception e) {
            log.error("验证刷新令牌异常: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 使用refreshToken获取新的token
     *
     * @param refreshToken 刷新令牌
     * @return 新的用户登录结果，包含新的token和refreshToken
     */
    public AppLoginResultVo refreshTokens(String refreshToken) {
        String userId = verifyRefreshToken(refreshToken);
        if (StringUtils.isEmpty(userId)) {
            throw new ServiceException("无效的刷新令牌");
        }
        
        // 从数据库获取用户信息
        QwxGlobalUser user = qwxGlobalUserService.lambdaQuery()
                .eq(QwxGlobalUser::getGlobalUserId, userId)
                .eq(QwxGlobalUser::getDelFlag, "0")
                .one();
                
        if (user == null) {
            throw new ServiceException("用户不存在或已被删除");
        }
        
        // 生成新的token
        return generateLoginResult(user);
    }

    /**
     * 生成用户登录结果
     *
     * @param globalUser 全局用户对象
     * @return 用户登录结果
     */
    public AppLoginResultVo generateLoginResult(QwxGlobalUser globalUser) {
        // 1. 获取所有租户和云信账号信息（合并查询）
        List<AppUserTenantIMVo.TenantIMInfo> tenantInfoList = qwxUserTenantMapper.selectUserTenantAndIMInfoList(globalUser.getGlobalUserId());

        // 2. 为每个租户生成对应的token
        if (tenantInfoList != null && !tenantInfoList.isEmpty()) {
            for (AppUserTenantIMVo.TenantIMInfo tenantInfo : tenantInfoList) {
                // 为每个租户创建对应的token
                Map<String, Object> tokenInfo = this.createTenantToken(globalUser, tenantInfo);
                // 将token设置到租户信息中
                tenantInfo.setToken((String) tokenInfo.get("access_token"));
            }
        }

        final Map<String, Object> token = this.createToken(globalUser);

        // 3. 使用AppLoginResultVo封装登录结果
        return AppLoginResultVo.builder()
                .expiresIn(expireTime * 60) // 转换为秒
                .token(token.get("access_token").toString())
                .refreshToken(token.get("refresh_token").toString())
                .globalUserId(globalUser.getGlobalUserId())
                .userName(globalUser.getUserName())
                .phone(globalUser.getPhone())
                .email(globalUser.getEmail())
                .sex(globalUser.getSex())
                .registerType(globalUser.getRegisterType())
                .tenantInfoList(tenantInfoList)
                .build();
    }

    /**
     * 生成单个租户的登录信息(含token)
     *
     * @param globalUserId 全局用户ID
     * @param tenantId     租户ID
     * @return 租户登录信息
     */
    public AppUserTenantIMVo.TenantIMInfo generateSingleTenantLoginInfo(String globalUserId, String tenantId) {
        // 获取全局用户信息
        QwxGlobalUser globalUser = qwxGlobalUserService.lambdaQuery()
                .eq(QwxGlobalUser::getGlobalUserId, globalUserId)
                .last("LIMIT 1")
                .one();

        if (globalUser == null) {
            throw new ServiceException("用户不存在");
        }

        // 获取指定租户和云信账号信息
        AppUserTenantIMVo.TenantIMInfo tenantInfo = qwxUserTenantMapper.selectSingleUserTenantAndIMInfo(globalUserId, tenantId);

        if (tenantInfo == null) {
            throw new ServiceException("未找到租户信息");
        }

        // 生成对应的token
        Map<String, Object> tokenInfo = this.createTenantToken(globalUser, tenantInfo);
        // 设置token到租户信息中
        tenantInfo.setToken((String) tokenInfo.get("access_token"));

        return tenantInfo;
    }
}
