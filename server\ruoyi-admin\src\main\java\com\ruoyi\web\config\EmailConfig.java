package com.ruoyi.web.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 邮件配置类
 * 
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "email")
public class EmailConfig {
    /** 是否启用 */
    private boolean enabled = true;

    /** 邮件服务器主机名 */
    private String host;

    /** 邮件服务器端口 */
    private int port = 25;

    /** 发件人地址 */
    private String from;

    /** 发件人用户名 */
    private String username;

    /** 发件人密码 */
    private String password;

    /** 是否启用SSL */
    private boolean ssl = false;

    /** 邮件主题 */
    private String subject = "验证码";

    /** 邮件内容模板 */
    private String template = "您的验证码是：%s，有效期%d分钟，请勿泄露给他人。";

    /** 邮件发送超时时间（秒） */
    private int timeout = 10;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isSsl() {
        return ssl;
    }

    public void setSsl(boolean ssl) {
        this.ssl = ssl;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }
} 