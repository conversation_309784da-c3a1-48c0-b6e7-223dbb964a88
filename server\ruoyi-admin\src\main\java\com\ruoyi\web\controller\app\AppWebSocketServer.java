package com.ruoyi.web.controller.app;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.web.config.GetHttpSessionConfigurator;
import com.ruoyi.web.service.AppUserTokenService;
import com.ruoyi.web.service.ClientStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * WebSocket服务器端点
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ServerEndpoint(value = "/app/ws", configurator = GetHttpSessionConfigurator.class)
public class AppWebSocketServer {

    /**
     * 静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。
     */
    private static int onlineCount = 0;

    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的WebSocket对象。
     */
    private static final ConcurrentHashMap<String, AppWebSocketServer> WEB_SOCKET_MAP = new ConcurrentHashMap<>();

    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;

    /**
     * 接收userId
     */
    private String userId;

    private String token;

    /**
     * 接收tenantId
     */
    private String tenantId;

    /**
     * 客户端状态服务
     */
    private static ClientStatusService clientStatusService;

    /**
     * APP用户Token服务
     */
    private static AppUserTokenService appUserTokenService;

    /**
     * 定时任务服务
     */
    private static final ScheduledExecutorService SCHEDULED_EXECUTOR_SERVICE = Executors.newScheduledThreadPool(1);

    static {
        // 启动定时任务，每30秒清理一次超时连接
        SCHEDULED_EXECUTOR_SERVICE.scheduleAtFixedRate(() -> {
            try {
                if (clientStatusService != null) {
                    clientStatusService.cleanExpiredClients();
                }
            } catch (Exception e) {
                log.error("清理超时连接异常", e);
            }
        }, 30, 30, TimeUnit.SECONDS);
    }

    @Autowired
    public void setClientStatusService(ClientStatusService clientStatusService) {
        AppWebSocketServer.clientStatusService = clientStatusService;
    }

    @Autowired
    public void setAppUserTokenService(AppUserTokenService appUserTokenService) {
        AppWebSocketServer.appUserTokenService = appUserTokenService;
    }

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, EndpointConfig config) {
        this.session = session;

        // 从请求参数获取token
        System.out.println("WebSocket connection opened: " + session.getId());

        // 从 Session 的用户属性中获取在 Configurator 中存储的 token
        Map<String, Object> userProperties = session.getUserProperties();
        this.token = (String) userProperties.get("AUTH_TOKEN");

        // 如果仍然没有找到token，则关闭连接
        if (token == null) {
            try {
                JSONObject result = new JSONObject();
                result.put("type", "error");
                result.put("message", "未提供身份验证Token");
                sendMessage(JSON.toJSONString(result));
                session.close();
            } catch (IOException e) {
                log.error("关闭未认证连接异常", e);
            }
            return;
        }

        // 解析token获取用户信息和租户信息
        try {
            // 通过token获取租户ID
            Long tenantId = appUserTokenService.getTenantIdFromToken(token);
            if (tenantId == null) {
                log.error("Token中租户ID为空");
                closeWithError(session, "Token无效或已过期");
                return;
            }

            // 通过token获取用户ID
            String userId = appUserTokenService.getUserIdFromToken(token);
            if (userId == null) {
                log.error("Token中用户ID为空");
                closeWithError(session, "Token无效或已过期");
                return;
            }

            // 验证token有效性
            if (!appUserTokenService.checkTenantToken(token)) {
                log.error("Token无效或已过期");
                closeWithError(session, "Token无效或已过期");
                return;
            }

            // 设置用户ID和租户ID
            this.userId = userId;
            this.tenantId = tenantId.toString();

            // 生成唯一连接标识符
            String userKey = tenantId + ":" + userId;

            // 加入集合
            WEB_SOCKET_MAP.put(userKey, this);
            addOnlineCount();

            log.info("租户[{}]用户[{}]连接成功，当前在线人数为:{}", tenantId, userId, getOnlineCount());

            // 注册客户端连接
            clientStatusService.registerClient(tenantId.toString(), userId, session.getId());

            try {
                JSONObject result = new JSONObject();
                result.put("type", "connected");
                result.put("tenantId", tenantId);
                result.put("userId", userId);
                result.put("onlineCount", getOnlineCount());
                sendMessage(JSON.toJSONString(result));
            } catch (IOException e) {
                log.error("租户[{}]用户[{}]连接异常", tenantId, userId, e);
            }
        } catch (Exception e) {
            log.error("解析Token异常", e);
            closeWithError(session, "Token解析失败");
        }
    }

    /**
     * 关闭连接并发送错误消息
     *
     * @param session      会话
     * @param errorMessage 错误消息
     */
    private void closeWithError(Session session, String errorMessage) {
        try {
            JSONObject result = new JSONObject();
            result.put("type", "error");
            result.put("message", errorMessage);
            sendMessage(JSON.toJSONString(result));
            session.close();
        } catch (IOException ioException) {
            log.error("关闭连接异常", ioException);
        }
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        // 生成唯一连接标识符
        String userKey = tenantId + ":" + userId;

        // 从集合中删除
        WEB_SOCKET_MAP.remove(userKey);
        subOnlineCount();

        // 注销客户端连接
        if (session != null) {
            clientStatusService.unregisterClient(session.getId());
        }

        log.info("租户[{}]用户[{}]退出，当前在线人数为:{}", tenantId, userId, getOnlineCount());
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.debug("租户[{}]用户[{}]发送消息:{}", tenantId, userId, message);

        try {
            JSONObject jsonObject = JSON.parseObject(message);
            String type = jsonObject.getString("type");

            // 处理心跳消息
            if ("heartbeat".equals(type) || "heartbeat_ping".equals(type) || "heartbeat_pong".equals(type)) {
                // 更新心跳时间
                clientStatusService.updateHeartbeat(session.getId());

                // 响应心跳确认
                JSONObject result = new JSONObject();
                result.put("type", "heartbeat_ack");
                result.put("time", System.currentTimeMillis());
                sendMessage(JSON.toJSONString(result));
            }
        } catch (Exception e) {
            log.error("处理消息异常", e);
            JSONObject result = new JSONObject();
            result.put("type", "error");
            result.put("message", "消息格式错误,请参考ws心跳接口文档");
            try {
                sendMessage(JSON.toJSONString(result));
            } catch (IOException e1) {
                log.error("发送消息异常", e1);
            }
        }
    }

    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("租户[{}]用户[{}]发生错误", tenantId, userId, error);

        // 注销客户端连接
        clientStatusService.unregisterClient(session.getId());
    }

    /**
     * 发送消息
     *
     * @param message 消息
     * @throws IOException IO异常
     */
    private void sendMessage(String message) throws IOException {
        this.session.getBasicRemote()
                .sendText(message);
    }

    /**
     * 群发自定义消息
     */
    public static void sendMessageToAll(String message) {
        for (String key : WEB_SOCKET_MAP.keySet()) {
            try {
                WEB_SOCKET_MAP.get(key)
                        .sendMessage(message);
            } catch (IOException e) {
                log.error("发送消息异常", e);
            }
        }
    }

    /**
     * 发送消息到指定租户的指定用户
     *
     * @param tenantId 租户ID
     * @param userId   用户ID
     * @param message  消息
     */
    public static void sendMessageToUser(String tenantId, String userId, String message) {
        String userKey = tenantId + ":" + userId;
        if (WEB_SOCKET_MAP.containsKey(userKey)) {
            try {
                WEB_SOCKET_MAP.get(userKey)
                        .sendMessage(message);
            } catch (IOException e) {
                log.error("发送消息给租户[{}]用户[{}]异常", tenantId, userId, e);
            }
        } else {
            log.warn("租户[{}]用户[{}]不在线", tenantId, userId);
        }
    }

    /**
     * 发送消息到指定租户的所有用户
     *
     * @param tenantId 租户ID
     * @param message  消息
     */
    public static void sendMessageToTenant(String tenantId, String message) {
        for (String key : WEB_SOCKET_MAP.keySet()) {
            if (key.startsWith(tenantId + ":")) {
                try {
                    WEB_SOCKET_MAP.get(key)
                            .sendMessage(message);
                } catch (IOException e) {
                    log.error("发送消息给租户[{}]异常", tenantId, e);
                }
            }
        }
    }

    /**
     * 检查用户是否在线
     *
     * @param tenantId 租户ID
     * @param userId   用户ID
     * @return 是否在线
     */
    public static boolean isUserOnline(String tenantId, String userId) {
        return clientStatusService.isClientOnline(tenantId, userId);
    }

    public static synchronized int getOnlineCount() {
        return onlineCount;
    }

    public static synchronized void addOnlineCount() {
        AppWebSocketServer.onlineCount++;
    }

    public static synchronized void subOnlineCount() {
        AppWebSocketServer.onlineCount--;
    }
} 