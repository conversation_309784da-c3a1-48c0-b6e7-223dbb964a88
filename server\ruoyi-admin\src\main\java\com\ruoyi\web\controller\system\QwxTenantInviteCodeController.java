package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.app.domain.QwxTenantInviteCode;
import com.ruoyi.app.service.QwxTenantInviteCodeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 租户邀请码Controller
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@RestController
@RequestMapping("/system/code")
public class QwxTenantInviteCodeController extends BaseController
{
    @Autowired
    private QwxTenantInviteCodeService qwxTenantInviteCodeService;

    /**
     * 查询租户邀请码列表
     */
    @PreAuthorize("@ss.hasPermi('system:code:list')")
    @GetMapping("/list")
    public TableDataInfo list(QwxTenantInviteCode qwxTenantInviteCode)
    {
        startPage();
        List<QwxTenantInviteCode> list = qwxTenantInviteCodeService.selectQwxTenantInviteCodeList(qwxTenantInviteCode);
        return getDataTable(list);
    }

    /**
     * 导出租户邀请码列表
     */
    @PreAuthorize("@ss.hasPermi('system:code:export')")
    @Log(title = "租户邀请码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QwxTenantInviteCode qwxTenantInviteCode)
    {
        List<QwxTenantInviteCode> list = qwxTenantInviteCodeService.selectQwxTenantInviteCodeList(qwxTenantInviteCode);
        ExcelUtil<QwxTenantInviteCode> util = new ExcelUtil<>(QwxTenantInviteCode.class);
        util.exportExcel(response, list, "租户邀请码数据");
    }

    /**
     * 获取租户邀请码详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:code:query')")
    @GetMapping(value = "/{codeId}")
    public AjaxResult getInfo(@PathVariable("codeId") Long codeId)
    {
        return success(qwxTenantInviteCodeService.getById(codeId));
    }

    /**
     * 新增租户邀请码
     */
    @PreAuthorize("@ss.hasPermi('system:code:add')")
    @Log(title = "租户邀请码", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QwxTenantInviteCode qwxTenantInviteCode)
    {
        return toAjax(qwxTenantInviteCodeService.save(qwxTenantInviteCode));
    }

    /**
     * 修改租户邀请码
     */
    @PreAuthorize("@ss.hasPermi('system:code:edit')")
    @Log(title = "租户邀请码", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QwxTenantInviteCode qwxTenantInviteCode)
    {
        return toAjax(qwxTenantInviteCodeService.saveOrUpdate(qwxTenantInviteCode));
    }

    /**
     * 删除租户邀请码
     */
    @PreAuthorize("@ss.hasPermi('system:code:remove')")
    @Log(title = "租户邀请码", businessType = BusinessType.DELETE)
	@DeleteMapping("/{codeIds}")
    public AjaxResult remove(@PathVariable Long[] codeIds)
    {
        return toAjax(qwxTenantInviteCodeService.removeById(codeIds));
    }
}
