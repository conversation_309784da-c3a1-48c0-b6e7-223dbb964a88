-- ----------------------------
-- 1、全局用户表：存储跨租户的用户基本信息
-- ----------------------------
drop table if exists qwx_global_user;
create table qwx_global_user (
  global_user_id       varchar(64)     not null                  comment '全局用户ID',
  user_name            varchar(30)     not null                  comment '用户账号（手机号或邮箱）',
  password             varchar(100)    default ''                comment '密码',
  phonenumber          varchar(11)     default ''                comment '手机号码',
  email                varchar(50)     default ''                comment '用户邮箱',
  sex                  char(1)         default '0'               comment '用户性别（0男 1女 2未知）',
  is_perfect_info      char(1)         default '0'               comment '是否完善信息（0未完善 1已完善）',
  register_type        varchar(10)     default 'phone'           comment '注册类型（phone手机号 email邮箱）',
  register_time        datetime                                  comment '注册时间',
  register_ip          varchar(128)    default ''                comment '注册IP',
  last_login_time      datetime                                  comment '最后登录时间',
  last_login_ip        varchar(128)    default ''                comment '最后登录IP',
  status               char(1)         default '0'               comment '账号状态（0正常 1停用）',
  del_flag             char(1)         default '0'               comment '删除标志（0存在 2删除）',
  wx_openid            varchar(64)     default NULL              comment '微信openid',
  wx_unionid           varchar(64)     default NULL              comment '微信unionid',
  create_time          datetime                                  comment '创建时间',
  update_time          datetime                                  comment '更新时间',
  remark               varchar(500)    default null              comment '备注',
  primary key (global_user_id),
  unique key uk_user_name (user_name)
) engine=innodb auto_increment=1 comment = '全局用户表';

-- ----------------------------
-- 2、租户表：存储企业/组织信息
-- ----------------------------
drop table if exists qwx_tenant;
create table qwx_tenant (
  tenant_id            bigint(20)      not null auto_increment   comment '租户ID',
  tenant_name          varchar(100)    not null                  comment '租户名称',
  tenant_code          varchar(50)     not null                  comment '租户编码，唯一标识',
  logo                 varchar(200)    default ''                comment '企业logo',
  industry             varchar(50)     default ''                comment '行业',
  scale                varchar(20)     default ''                comment '企业规模',
  address              varchar(200)    default ''                comment '企业地址',
  contact_user         varchar(50)     default ''                comment '联系人',
  contact_phone        varchar(20)     default ''                comment '联系电话',
  domain               varchar(100)    default ''                comment '域名',
  intro                varchar(500)    default ''                comment '企业介绍',
  status               char(1)         default '0'               comment '状态（0正常 1停用）',
  del_flag             char(1)         default '0'               comment '删除标志（0存在 2删除）',
  expire_time          datetime                                  comment '过期时间',
  account_count        int             default 0                 comment '账号数量',
  create_user_id       varchar(64)                               comment '创建人ID（关联全局用户ID）',
  create_time          datetime                                  comment '创建时间',
  update_time          datetime                                  comment '更新时间',
  remark               varchar(500)    default null              comment '备注',
  primary key (tenant_id),
  unique key uk_tenant_code (tenant_code)
) engine=innodb auto_increment=1 comment = '租户表';

-- ----------------------------
-- 3、用户租户关系表：用户与租户的关联关系
-- ----------------------------
drop table if exists qwx_user_tenant;
create table qwx_user_tenant (
  id                   bigint(20)      not null auto_increment   comment 'ID',
  global_user_id       varchar(64)     not null                  comment '全局用户ID',
  tenant_id            bigint(20)      not null                  comment '租户ID',
  user_id              bigint(20)      not null                  comment '租户内用户ID',
  nick_name            varchar(30)                               comment '租户内昵称',
  avatar               varchar(100)    default ''                comment '头像地址',
  position             varchar(50)     default ''                comment '职位',
  join_time            datetime                                  comment '加入时间',
  status               char(1)         default '0'               comment '状态（0正常 1停用 2待审核）',
  online_status        char(1)         default '0'               comment '在线状态（0离线 1在线 2隐身）',
  is_admin             char(1)         default '0'               comment '是否为管理员（0否 1是）',
  create_time          datetime                                  comment '创建时间',
  update_time          datetime                                  comment '更新时间',
  is_quit              char(1)         default '0'               comment '是否已退出组织（0否 1是）',
  primary key (id),
  unique key uk_user_tenant (global_user_id, tenant_id)
) engine=innodb auto_increment=1 comment = '用户租户关系表';

-- ----------------------------
-- 4、租户配置表：存储组织参数配置信息
-- ----------------------------
drop table if exists qwx_tenant_config;
create table qwx_tenant_config (
  config_id            bigint(20)      not null auto_increment   comment '配置ID',
  tenant_id            bigint(20)      not null                  comment '租户ID',
  config_key           varchar(100)    not null                  comment '配置键名',
  config_value         varchar(500)    default ''                comment '配置值',
  config_type          varchar(50)     default 'text'            comment '配置类型（text文本、number数字、boolean布尔、select选择、image图片）',
  is_default           char(1)         default 'N'               comment '是否系统默认（Y是 N否）',
  config_name          varchar(100)    not null                  comment '配置名称',
  config_desc          varchar(500)    default ''                comment '配置描述',
  options              varchar(1000)   default ''                comment '可选项（适用于select类型，JSON格式）',
  order_num            int             default 0                 comment '显示顺序',
  status               char(1)         default '0'               comment '状态（0正常 1停用）',
  create_time          datetime                                  comment '创建时间',
  update_time          datetime                                  comment '更新时间',
  create_by            varchar(64)     default ''                comment '创建者',
  update_by            varchar(64)     default ''                comment '更新者',
  remark               varchar(500)    default null              comment '备注',
  primary key (config_id),
  unique key uk_tenant_config (tenant_id, config_key)
) engine=innodb auto_increment=1 comment = '租户配置表';

-- 插入默认配置项
insert into qwx_tenant_config values(1, 1, 'auto_accept_friend', 'N', 'boolean', 'Y', '自动通过好友申请', '是否自动通过所有好友申请', '', 1, '0', sysdate(), null, 'admin', '', null);
insert into qwx_tenant_config values(2, 1, 'auto_join_group', 'N', 'boolean', 'Y', '自动加入大群', '是否自动将新用户加入企业大群', '', 2, '0', sysdate(), null, 'admin', '', null);
insert into qwx_tenant_config values(3, 1, 'admin_create_group_only', 'N', 'boolean', 'Y', '仅管理员创建群聊', '是否只允许管理员创建群聊', '', 3, '0', sysdate(), null, 'admin', '', null);

-- ----------------------------
-- 5、好友关系表
-- ----------------------------
drop table if exists im_friend;
create table im_friend (
  id                   bigint(20)      not null auto_increment   comment 'ID',
  tenant_id            bigint(20)      not null                  comment '租户ID',
  user_id              bigint(20)      not null                  comment '用户ID',
  friend_id            bigint(20)      not null                  comment '好友ID',
  remark_name          varchar(50)     default ''                comment '备注名',
  status               char(1)         default '0'               comment '状态（0正常 1特别关注 2拉黑）',
  create_time          datetime                                  comment '创建时间',
  update_time          datetime                                  comment '更新时间',
  primary key (id),
  unique key uk_user_friend (tenant_id, user_id, friend_id)
) engine=innodb auto_increment=1 comment = '好友关系表';

-- ----------------------------
-- 6、群组表
-- ----------------------------
drop table if exists im_group;
create table im_group (
  group_id             bigint(20)      not null auto_increment   comment '群组ID',
  tenant_id            bigint(20)      not null                  comment '租户ID',
  group_name           varchar(100)    not null                  comment '群组名称',
  group_avatar         varchar(200)    default ''                comment '群组头像',
  owner_id             bigint(20)      not null                  comment '群主ID',
  introduction         varchar(500)    default ''                comment '群介绍',
  announcement         varchar(1000)   default ''                comment '群公告',
  member_count         int             default 0                 comment '成员数量',
  max_member_count     int             default 200               comment '最大成员数',
  join_mode            char(1)         default '0'               comment '加入方式（0自由加入 1需审批 2禁止加入）',
  status               char(1)         default '0'               comment '状态（0正常 1解散）',
  create_time          datetime                                  comment '创建时间',
  update_time          datetime                                  comment '更新时间',
  primary key (group_id)
) engine=innodb auto_increment=1 comment = '群组表';

-- ----------------------------
-- 7、群组成员表
-- ----------------------------
drop table if exists im_group_member;
create table im_group_member (
  id                   bigint(20)      not null auto_increment   comment 'ID',
  tenant_id            bigint(20)      not null                  comment '租户ID',
  group_id             bigint(20)      not null                  comment '群组ID',
  user_id              bigint(20)      not null                  comment '用户ID',
  nickname             varchar(50)     default ''                comment '群内昵称',
  role                 char(1)         default '0'               comment '角色（0普通成员 1管理员 2群主）',
  join_time            datetime                                  comment '加入时间',
  mute_end_time        datetime                                  comment '禁言结束时间',
  status               char(1)         default '0'               comment '状态（0正常 1退出）',
  create_time          datetime                                  comment '创建时间',
  update_time          datetime                                  comment '更新时间',
  primary key (id),
  unique key uk_group_user (tenant_id, group_id, user_id)
) engine=innodb auto_increment=1 comment = '群组成员表';

-- ----------------------------
-- 8、消息表
-- ----------------------------
drop table if exists im_message;
create table im_message (
  msg_id               bigint(20)      not null auto_increment   comment '消息ID',
  tenant_id            bigint(20)      not null                  comment '租户ID',
  msg_type             char(1)         not null                  comment '消息类型（0单聊 1群聊）',
  content_type         char(2)         not null                  comment '内容类型（00文本 01图片 02语音 03视频 04文件）',
  from_id              bigint(20)      not null                  comment '发送者ID',
  to_id                bigint(20)      not null                  comment '接收者ID（用户ID或群组ID）',
  content              text                                      comment '消息内容',
  send_time            datetime        not null                  comment '发送时间',
  status               char(1)         default '0'               comment '状态（0未读 1已读 2撤回 3删除）',
  is_recall            char(1)         default '0'               comment '是否撤回（0否 1是）',
  create_time          datetime                                  comment '创建时间',
  yx_msg_id            varchar(64)     default ''                comment '云信消息ID',
  remote_extension     text                                      comment '服务端扩展字段',
  local_extension      text                                      comment '客户端扩展字段',
  attachment_info      text                                      comment '附件信息',
  msg_direction        char(1)         comment '消息方向',
  time_ms              bigint(20)      comment '毫秒级时间戳',
  primary key (msg_id),
  key idx_tenant_from_to (tenant_id, from_id, to_id)
) engine=innodb auto_increment=1 comment = '消息表';

-- ----------------------------
-- 9、消息已读表
-- ----------------------------
drop table if exists im_message_read;
create table im_message_read (
  id                   bigint(20)      not null auto_increment   comment 'ID',
  tenant_id            bigint(20)      not null                  comment '租户ID',
  msg_id               bigint(20)      not null                  comment '消息ID',
  user_id              bigint(20)      not null                  comment '用户ID',
  read_time            datetime        not null                  comment '阅读时间',
  create_time          datetime                                  comment '创建时间',
  primary key (id),
  unique key uk_msg_user (tenant_id, msg_id, user_id)
) engine=innodb auto_increment=1 comment = '消息已读表';

-- ----------------------------
-- 10、授权Token表：用于独立部署授权验证
-- ----------------------------
drop table if exists qwx_auth_token;
create table qwx_auth_token (
  token_id             bigint(20)      not null auto_increment   comment 'Token ID',
  tenant_id            bigint(20)      not null                  comment '租户ID',
  token                varchar(200)    not null                  comment '授权Token',
  issue_time           datetime        not null                  comment '签发时间',
  expire_time          datetime        not null                  comment '过期时间',
  status               char(1)         default '0'               comment '状态（0有效 1无效）',
  create_time          datetime                                  comment '创建时间',
  update_time          datetime                                  comment '更新时间',
  instance_id          bigint(20)      default NULL              comment '部署实例ID，关联qwx_deploy_instance表',
  public_ip            varchar(50)     default ''                comment '获取Token的公网IP',
  device_info          varchar(500)    default ''                comment '设备信息',
  last_verify_time     datetime        default NULL              comment '最后验证时间',
  primary key (token_id),
  unique key uk_tenant_token (tenant_id, token),
  key idx_instance_id (instance_id)
) engine=innodb auto_increment=1 comment = '授权Token表';

-- ----------------------------
-- 11、云信账号映射表：将系统用户与网易云信账号关联
-- ----------------------------
drop table if exists im_yxaccount_mapping;
create table im_yxaccount_mapping (
  id                   bigint(20)      not null auto_increment   comment 'ID',
  user_id              bigint(20)      not null                  comment '系统用户ID',
  global_user_id       varchar(64)     not null                  comment '全局用户ID',
  tenant_id            bigint(20)      not null                  comment '租户ID',
  yx_account_id        varchar(100)    not null                  comment '云信账号ID',
  yx_token             varchar(200)    default ''                comment '云信Token',
  status               char(1)         default '0'               comment '状态（0正常 1停用）',
  create_time          datetime                                  comment '创建时间',
  update_time          datetime                                  comment '更新时间',
  primary key (id),
  unique key uk_user_tenant (user_id, tenant_id),
  unique key uk_yx_account (yx_account_id)
) engine=innodb auto_increment=1 comment = '云信账号映射表';

-- ----------------------------
-- 12、部署实例表：存储独立部署的实例信息
-- ----------------------------
drop table if exists qwx_deploy_instance;
create table qwx_deploy_instance (
  instance_id          bigint(20)      not null auto_increment   comment '实例ID',
  tenant_id            bigint(20)      not null                  comment '租户ID',
  instance_name        varchar(100)    not null                  comment '实例名称',
  instance_code        varchar(50)     not null                  comment '实例编码，唯一标识',
  public_ip            varchar(50)     default ''                comment '公网IP',
  private_ip           varchar(50)     default ''                comment '内网IP',
  domain               varchar(100)    default ''                comment '访问域名',
  license_key          varchar(200)    not null                  comment '许可证密钥',
  deploy_env           varchar(20)     default 'prod'            comment '部署环境(dev开发,test测试,prod生产)',
  max_users            int             default 100               comment '最大用户数',
  version              varchar(20)     default ''                comment '当前版本号',
  heartbeat_time       datetime                                  comment '最近心跳时间',
  heartbeat_interval   int             default 300               comment '心跳间隔(秒)',
  status               char(1)         default '0'               comment '状态（0正常 1停用 2离线）',
  expire_time          datetime                                  comment '过期时间',
  offline_auth_days    int             default 7                 comment '离线授权天数',
  create_time          datetime                                  comment '创建时间',
  update_time          datetime                                  comment '更新时间',
  remark               varchar(500)    default null              comment '备注',
  primary key (instance_id),
  unique key uk_instance_code (instance_code),
  key idx_tenant_id (tenant_id)
) engine=innodb auto_increment=1 comment = '部署实例表';

-- ----------------------------
-- 13、系统实例心跳表：记录独立部署实例的心跳信息
-- ----------------------------
drop table if exists qwx_instance_heartbeat;
create table qwx_instance_heartbeat (
  id                  bigint(20)      not null auto_increment   comment 'ID',
  instance_id         bigint(20)      not null                  comment '实例ID',
  heartbeat_time      datetime        not null                  comment '心跳时间',
  ip_address          varchar(50)     default ''                comment 'IP地址',
  system_info         varchar(500)    default ''                comment '系统信息',
  cpu_usage           decimal(5,2)    default 0                 comment 'CPU使用率(%)',
  memory_usage        decimal(5,2)    default 0                 comment '内存使用率(%)',
  disk_usage          decimal(5,2)    default 0                 comment '磁盘使用率(%)',
  network_speed       varchar(50)     default ''                comment '网络速度',
  active_users        int             default 0                 comment '活跃用户数',
  online_status       char(1)         default '0'               comment '在线状态（0在线 1离线）',
  create_time         datetime                                  comment '创建时间',
  primary key (id),
  key idx_instance_heartbeat (instance_id, heartbeat_time)
) engine=innodb auto_increment=1 comment = '系统实例心跳表';

-- ----------------------------
-- 14、实例连接配置表：管理独立部署实例的连接信息
-- ----------------------------
drop table if exists qwx_instance_connection;
create table qwx_instance_connection (
  conn_id             bigint(20)      not null auto_increment   comment '连接ID',
  instance_id         bigint(20)      not null                  comment '实例ID',
  saas_api_url        varchar(200)    not null                  comment 'SaaS平台API地址',
  auth_endpoint       varchar(200)    not null                  comment '授权端点',
  heartbeat_endpoint  varchar(200)    not null                  comment '心跳端点',
  sync_endpoint       varchar(200)    default ''                comment '数据同步端点',
  api_key             varchar(100)    not null                  comment 'API密钥',
  api_secret          varchar(200)    not null                  comment 'API密钥秘钥',
  conn_timeout        int             default 10000             comment '连接超时(毫秒)',
  read_timeout        int             default 30000             comment '读取超时(毫秒)',
  retry_count         int             default 3                 comment '重试次数',
  retry_interval      int             default 5000              comment '重试间隔(毫秒)',
  encryption_type     varchar(20)     default 'AES'             comment '加密类型',
  encryption_key      varchar(200)    default ''                comment '加密密钥',
  status              char(1)         default '0'               comment '状态（0启用 1停用）',
  create_time         datetime                                  comment '创建时间',
  update_time         datetime                                  comment '更新时间',
  primary key (conn_id),
  unique key uk_instance_id (instance_id)
) engine=innodb auto_increment=1 comment = '实例连接配置表';

-- ----------------------------
-- 15、实例授权历史表：记录独立部署实例的授权历史
-- ----------------------------
drop table if exists qwx_instance_auth_history;
create table qwx_instance_auth_history (
  history_id          bigint(20)      not null auto_increment   comment '历史ID',
  instance_id         bigint(20)      not null                  comment '实例ID',
  token_id            bigint(20)      not null                  comment 'Token ID',
  auth_type           varchar(20)     not null                  comment '授权类型(init初始化,renew续期,revoke吊销)',
  auth_result         char(1)         not null                  comment '授权结果(0成功 1失败)',
  auth_time           datetime        not null                  comment '授权时间',
  expire_time         datetime                                  comment '过期时间',
  ip_address          varchar(50)     default ''                comment 'IP地址',
  error_msg           varchar(500)    default ''                comment '错误信息',
  create_time         datetime                                  comment '创建时间',
  primary key (history_id),
  key idx_instance_auth (instance_id, auth_time)
) engine=innodb auto_increment=1 comment = '实例授权历史表';

-- ----------------------------
-- 16、租户邀请码表：管理用于邀请新成员的邀请码
-- ----------------------------
drop table if exists qwx_tenant_invite_code;
create table qwx_tenant_invite_code (
  code_id              bigint(20)      not null auto_increment   comment '邀请码ID',
  tenant_id            bigint(20)      not null                  comment '租户ID',
  invite_code          varchar(20)     not null                  comment '邀请码',
  code_name            varchar(100)    default ''                comment '邀请码名称',
  creator_id           bigint(20)      not null                  comment '创建者ID',
  max_uses             int             default 0                 comment '最大使用次数（0表示不限制）',
  used_count           int             default 0                 comment '已使用次数',
  expire_time          datetime                                  comment '过期时间',
  status               char(1)         default '0'               comment '状态（0有效 1已禁用 2已过期 3已用尽）',
  auto_activate        char(1)         default '1'               comment '是否自动激活账号（0否 1是）',
  need_approval        char(1)         default '0'               comment '是否需要审核（0否 1是）',
  default_role         varchar(100)    default ''                comment '默认角色',
  create_time          datetime                                  comment '创建时间',
  update_time          datetime                                  comment '更新时间',
  remark               varchar(500)    default null              comment '备注',
  primary key (code_id),
  unique key uk_tenant_code (tenant_id, invite_code),
  key idx_expire_time (expire_time)
) engine=innodb auto_increment=1 comment = '租户邀请码表';

-- ----------------------------
-- 17、邀请码使用记录表：记录邀请码的使用情况
-- ----------------------------
drop table if exists qwx_invite_code_usage;
create table qwx_invite_code_usage (
  usage_id             bigint(20)      not null auto_increment   comment '使用记录ID',
  code_id              bigint(20)      not null                  comment '邀请码ID',
  tenant_id            bigint(20)      not null                  comment '租户ID',
  invite_code          varchar(20)     not null                  comment '邀请码',
  user_id              bigint(20)      not null                  comment '使用者用户ID',
  global_user_id       varchar(64)     not null                  comment '全局用户ID',
  use_time             datetime        not null                  comment '使用时间',
  ip_address           varchar(50)     default ''                comment 'IP地址',
  device_info          varchar(500)    default ''                comment '设备信息',
  status               char(1)         default '0'               comment '状态（0成功 1失败 2待审核）',
  error_msg            varchar(500)    default ''                comment '错误信息',
  create_time          datetime                                  comment '创建时间',
  primary key (usage_id),
  key idx_code_id (code_id),
  key idx_tenant_user (tenant_id, user_id)
) engine=innodb auto_increment=1 comment = '邀请码使用记录表';

-- ----------------------------
-- 18、邀请申请审核表：记录需要审核的邀请申请
-- ----------------------------
drop table if exists qwx_invite_approval;
create table qwx_invite_approval (
  approval_id          bigint(20)      not null auto_increment   comment '审核ID',
  usage_id             bigint(20)      not null                  comment '邀请码使用记录ID',
  tenant_id            bigint(20)      not null                  comment '租户ID',
  user_id              bigint(20)      not null                  comment '申请用户ID',
  global_user_id       varchar(64)     not null                  comment '全局用户ID',
  approver_id          bigint(20)                                comment '审核人ID',
  apply_time           datetime        not null                  comment '申请时间',
  approval_time        datetime                                  comment '审核时间',
  approval_status      char(1)         default '0'               comment '审核状态（0待审核 1已通过 2已拒绝）',
  rejection_reason     varchar(500)    default ''                comment '拒绝原因',
  apply_info           varchar(1000)   default ''                comment '申请信息（用户填写）',
  create_time          datetime                                  comment '创建时间',
  update_time          datetime                                  comment '更新时间',
  primary key (approval_id),
  key idx_tenant_status (tenant_id, approval_status),
  key idx_usage_id (usage_id)
) engine=innodb auto_increment=1 comment = '邀请申请审核表';

-- ----------------------------
-- 19、好友申请记录表：存储用户之间的好友申请信息
-- ----------------------------
drop table if exists im_friend_request;
create table im_friend_request (
  id                   bigint(20)      not null auto_increment   comment 'ID',
  tenant_id            bigint(20)      not null                  comment '租户ID',
  sender_user_id       bigint(20)      not null                  comment '申请发送者用户ID',
  receiver_user_id     bigint(20)      not null                  comment '申请接收者用户ID',
  request_message      varchar(200)    default ''                comment '申请留言',
  status               char(1)         default '0'               comment '申请状态（0待处理 1已同意 2已拒绝 3已过期）',
  request_time         datetime        not null                  comment '申请发送时间',
  response_time        datetime                                  comment '申请处理时间',
  create_time          datetime                                  comment '创建时间',
  update_time          datetime                                  comment '更新时间',
  primary key (id),
  unique key uk_sender_receiver (tenant_id, sender_user_id, receiver_user_id),
  key idx_receiver_status (tenant_id, receiver_user_id, status)
) engine=innodb auto_increment=1 comment = '好友申请记录表';

-- ----------------------------
-- 20、群组权限配置表：存储群组内不同角色或成员的权限配置
-- ----------------------------
drop table if exists im_group_permission_config;
create table im_group_permission_config (
  id                   bigint(20)      not null auto_increment   comment 'ID',
  tenant_id            bigint(20)      not null                  comment '租户ID',
  group_id             bigint(20)      not null                  comment '群组ID',
  role_type            char(1)         default '0'               comment '角色类型（0普通成员 1管理员 2群主 9自定义）',
  permission_key       varchar(100)    not null                  comment '权限键名（如：can_send_message, can_invite_member, can_kick_member, can_modify_group_info, can_manage_announcement, can_mute_member）',
  permission_value     char(1)         default '1'               comment '权限值（0禁止 1允许）',
  create_time          datetime                                  comment '创建时间',
  update_time          datetime                                  comment '更新时间',
  primary key (id),
  unique key uk_group_role_permission (tenant_id, group_id, role_type, permission_key)
) engine=innodb auto_increment=1 comment = '群组权限配置表';


