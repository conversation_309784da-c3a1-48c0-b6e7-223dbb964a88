package com.ruoyi.web.service;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.app.domain.QwxUserTenant;
import com.ruoyi.app.service.QwxUserTenantService;
import com.ruoyi.common.core.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 客户端状态管理服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ClientStatusService {

    /**
     * 用户会话集合
     * key: 租户ID:用户ID
     * value: {
     *   sessionId: 会话ID, 
     *   lastHeartbeatTime: 最后心跳时间, 
     *   tenantId: 租户ID,
     *   userId: 用户ID,
     *   heartbeatFailCount: 心跳失败次数
     * }
     */
    private static final Map<String, Map<String, Object>> USER_SESSIONS = new ConcurrentHashMap<>();
    
    /**
     * 会话ID与用户ID的映射
     * key: 会话ID
     * value: 租户ID:用户ID
     */
    private static final Map<String, String> SESSION_USER_MAPPING = new ConcurrentHashMap<>();
    
    /**
     * 心跳超时时间（毫秒）
     */
    private static final long HEARTBEAT_TIMEOUT = 45000;    
    
    /**
     * 在线状态 - 离线
     */
    private static final String ONLINE_STATUS_OFFLINE = "0";
    
    /**
     * 在线状态 - 在线
     */
    private static final String ONLINE_STATUS_ONLINE = "1";
    
    /**
     * 在线状态 - 隐身
     */
    private static final String ONLINE_STATUS_INVISIBLE = "2";
    
    @Resource
    private QwxUserTenantService qwxUserTenantService;
    
    /**
     * 构建用户键
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 用户键
     */
    private String buildUserKey(String tenantId, String userId) {
        return tenantId + ":" + userId;
    }

    /**
     * 注册客户端连接
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param sessionId 会话ID
     */
    public void registerClient(String tenantId, String userId, String sessionId) {
        String userKey = buildUserKey(tenantId, userId);
        
        Map<String, Object> sessionInfo = new ConcurrentHashMap<>();
        sessionInfo.put("sessionId", sessionId);
        sessionInfo.put("lastHeartbeatTime", System.currentTimeMillis());
        sessionInfo.put("tenantId", tenantId);
        sessionInfo.put("userId", userId);
        sessionInfo.put("heartbeatFailCount", 0); // 初始化心跳失败计数为0
                
        USER_SESSIONS.put(userKey, sessionInfo);
        SESSION_USER_MAPPING.put(sessionId, userKey);
        
        log.info("租户[{}]用户[{}]建立连接，会话ID:[{}]", tenantId, userId, sessionId);
        
        // 更新用户在线状态为在线
        updateUserOnlineStatus(userId, tenantId, ONLINE_STATUS_ONLINE);
    }
    
    /**
     * 注销客户端连接
     * 
     * @param sessionId 会话ID
     */
    public void unregisterClient(String sessionId) {
        String userKey = SESSION_USER_MAPPING.get(sessionId);
        if (userKey != null) {
            Map<String, Object> sessionInfo = USER_SESSIONS.get(userKey);
            if (sessionInfo != null) {
                String tenantId = (String) sessionInfo.get("tenantId");
                String userId = (String) sessionInfo.get("userId");
                USER_SESSIONS.remove(userKey);
                SESSION_USER_MAPPING.remove(sessionId);
                log.info("租户[{}]用户[{}]断开连接，会话ID:[{}]", tenantId, userId, sessionId);
                
                // 更新用户在线状态为离线
                updateUserOnlineStatus(userId, tenantId, ONLINE_STATUS_OFFLINE);
            }
        }
    }
    
    /**
     * 更新客户端心跳时间
     * 
     * @param sessionId 会话ID
     */
    public void updateHeartbeat(String sessionId) {
        String userKey = SESSION_USER_MAPPING.get(sessionId);
        if (userKey != null) {
            Map<String, Object> sessionInfo = USER_SESSIONS.get(userKey);
            if (sessionInfo != null) {
                sessionInfo.put("lastHeartbeatTime", System.currentTimeMillis());
                sessionInfo.put("heartbeatFailCount", 0); // 重置心跳失败计数
                String tenantId = (String) sessionInfo.get("tenantId");
                String userId = (String) sessionInfo.get("userId");
                log.debug("租户[{}]用户[{}]心跳更新，会话ID:[{}]", tenantId, userId, sessionId);
            }
        }
    }

    /**
     * 检查客户端是否在线
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 是否在线
     */
    public boolean isClientOnline(String tenantId, String userId) {
        String userKey = buildUserKey(tenantId, userId);
        Map<String, Object> sessionInfo = USER_SESSIONS.get(userKey);
        if (sessionInfo == null) {
            return false;
        }
        
        long lastHeartbeatTime = (long) sessionInfo.get("lastHeartbeatTime");
        
        // 检查最后心跳时间是否在超时范围内
        return (System.currentTimeMillis() - lastHeartbeatTime <= HEARTBEAT_TIMEOUT);
    }
    
    /**
     * 获取在线用户数
     * 
     * @return 在线用户数
     */
    public int getOnlineCount() {
        return USER_SESSIONS.size();
    }
    
    /**
     * 获取指定租户的在线用户数
     *
     * @param tenantId 租户ID
     * @return 在线用户数
     */
    public int getOnlineCountByTenant(String tenantId) {
        return (int) USER_SESSIONS.entrySet().stream()
                .filter(e -> {
                    Map<String, Object> sessionInfo = e.getValue();
                    return tenantId.equals(sessionInfo.get("tenantId"));
                })
                .count();
    }
    
    /**
     * 清理超时的客户端连接
     */
    public void cleanExpiredClients() {
        long currentTime = System.currentTimeMillis();
        
        USER_SESSIONS.forEach((userKey, sessionInfo) -> {
            long lastHeartbeatTime = (long) sessionInfo.get("lastHeartbeatTime");
            String sessionId = (String) sessionInfo.get("sessionId");
            String tenantId = (String) sessionInfo.get("tenantId");
            String userId = (String) sessionInfo.get("userId");
            
            // 检查心跳是否超时（超过45秒没有收到心跳）
            if (currentTime - lastHeartbeatTime > HEARTBEAT_TIMEOUT) {
                log.info("清理超时连接：租户[{}]用户[{}]，会话ID:[{}]，超过[{}]毫秒未收到心跳", 
                        tenantId, userId, sessionId, HEARTBEAT_TIMEOUT);
                
                USER_SESSIONS.remove(userKey);
                SESSION_USER_MAPPING.remove(sessionId);
                
                // 更新用户在线状态为离线
                updateUserOnlineStatus(userId, tenantId, ONLINE_STATUS_OFFLINE);
            }
        });
    }
    
    /**
     * 更新用户在线状态
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @param onlineStatus 在线状态（0离线 1在线 2隐身）
     */
    private void updateUserOnlineStatus(String userId, String tenantId, String onlineStatus) {
        try {
            // 先查询用户当前的在线状态
            QwxUserTenant userTenant = qwxUserTenantService.lambdaQuery()
                    .eq(QwxUserTenant::getGlobalUserId, userId)
                    .eq(QwxUserTenant::getTenantId, tenantId)
                    .one();
            
            // 如果用户不存在，或者用户当前是隐身状态(2)且要设置为离线(0)或在线(1)，则不更新状态
            if (userTenant == null || 
                (ONLINE_STATUS_INVISIBLE.equals(userTenant.getOnlineStatus()) && 
                !ONLINE_STATUS_INVISIBLE.equals(onlineStatus))) {
                log.info("用户[{}]当前为隐身状态，不更新为[{}]状态", userId, onlineStatus);
                return;
            }
            
            // 更新QwxUserTenant表中的在线状态
            boolean updated = qwxUserTenantService.lambdaUpdate()
                    .eq(QwxUserTenant::getGlobalUserId, userId)
                    .eq(QwxUserTenant::getTenantId, tenantId)
                    .set(QwxUserTenant::getOnlineStatus, onlineStatus)
                    .update();
            
            if (updated) {
                log.info("已更新租户[{}]用户[{}]的在线状态为[{}]", tenantId, userId, onlineStatus);
            } else {
                log.warn("更新租户[{}]用户[{}]的在线状态失败", tenantId, userId);
            }
        } catch (Exception e) {
            log.error("更新用户在线状态异常", e);
        }
    }
    
    /**
     * 设置用户为隐身状态
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 是否设置成功
     */
    public boolean setUserInvisible(String userId, String tenantId) {
        try {
            updateUserOnlineStatus(userId, tenantId, ONLINE_STATUS_INVISIBLE);
            return true;
        } catch (Exception e) {
            log.error("设置用户隐身状态异常", e);
            return false;
        }
    }
    
    /**
     * 设置用户为在线状态
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 是否设置成功
     */
    public boolean setUserOnline(String userId, String tenantId) {
        String userKey = buildUserKey(tenantId, userId);
        if (USER_SESSIONS.containsKey(userKey)) {
            try {
                updateUserOnlineStatus(userId, tenantId, ONLINE_STATUS_ONLINE);
                return true;
            } catch (Exception e) {
                log.error("设置用户在线状态异常", e);
                return false;
            }
        }
        return false;
    }
} 